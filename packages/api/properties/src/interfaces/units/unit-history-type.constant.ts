export enum MyndUnitHistoryType {
  UnitAssetId = 'UNIT_ASSET_ID',
  UnitAddress = 'UNIT_ADDRESS',
  UnitNumber = 'UNIT_NUMBER',
  UnitCity = 'UNIT_CITY',
  UnitState = 'UNIT_STATE',
  UnitCounty = 'UNIT_COUNTY',
  UnitZip = 'UNIT_ZIP',
  UnitFloorPlan = 'UNIT_FLOOR_PLAN',
  UnitBedroomsNumber = 'UNIT_BEDROOMS_NUMBER',
  UnitFullBathroomsNumber = 'UNIT_FULL_BATHROOMS_NUMBER',
  UnitHalfBathroomsNumber = 'UNIT_HALF_BATHROOMS_NUMBER',
  UnitSquareFootage = 'UNIT_SQUARE_FOOTAGE',
  UnitFloor = 'UNIT_FLOOR',
  UnitMailboxNumber = 'UNIT_MAILBOX_NUMBER',
  UnitParkingSpaceType = 'UNIT_PARKING_SPACE_TYPE',
  UnitParkingSpaceNumber = 'UNIT_PARKING_SPACE_NUMBER',
  UnitParkingSpaceUncoveredAmount = 'UNIT_PARKING_SPACE_UNCOVERED_AMOUNT',
  UnitParkingSpaceCoveredAmount = 'UNIT_PARKING_SPACE_COVERED_AMOUNT',
  UnitVirtualTourLink = 'UNIT_VIRTUAL_TOUR_LINK',
  UnitPhotosLink = 'UNIT_PHOTOS_LINK',
  UnitSmartlockSerial = 'UNIT_SMARTLOCK_SERIAL',
  UnitOrder = 'UNIT_ORDER',
  UnitStatus = 'UNIT_STATUS',
  UnitStatusV2 = 'UNIT_STATUS_V2',
  UnitStatusChangeReason = 'UNIT_STATUS_CHANGE_REASON',
  UnitStatusReasonCode = 'UNIT_STATUS_REASON_CODE',
  UnitOccupied = 'UNIT_OCCUPIED',
  UnitRenovationState = 'UNIT_RENOVATION_STATE',
  UnitRentalDetailVacantReadyDate = 'UNIT_RENTAL_DETAIL_VACANT_READY_DATE',
  UnitRentalDetailActualVacantReadyDate = 'UNIT_RENTAL_DETAIL_ACTUAL_VACANT_READY_DATE',
  UnitRentalDetailVacantDownDate = 'UNIT_RENTAL_DETAIL_VACANT_DOWN_DATE',
  UnitRentalDetailTargetRentAmount = 'UNIT_RENTAL_DETAIL_TARGET_RENT_AMOUNT',
  UnitRentalDetailParkingRent = 'UNIT_RENTAL_DETAIL_PARKING_RENT',
  UnitRentalDetailMinLeaseTermType = 'UNIT_RENTAL_DETAIL_MIN_LEASE_TERM_TYPE',
  UnitRentalDetailMarketingDescription = 'UNIT_RENTAL_DETAIL_MARKETING_DESCRIPTION',
  UnitRentalDetailMarketingHeader = 'UNIT_RENTAL_DETAIL_MARKETING_HEADER',
  UnitRentalDetailConcessions = 'UNIT_RENTAL_DETAIL_CONCESSIONS',
  UnitRentalDetailFlatFeeAmount = 'UNIT_RENTAL_DETAIL_FLAT_FEE_AMOUNT',
  UnitRentalDetailHotUnit = 'UNIT_RENTAL_DETAIL_HOT_UNIT',
  UnitRentalDetailPredictedRentMin = 'UNIT_RENTAL_DETAIL_PREDICTED_RENT_MIN',
  UnitRentalDetailPredictedRentMax = 'UNIT_RENTAL_DETAIL_PREDICTED_RENT_MAX',
  UnitRentalDetailPredictedRentApprovedByOwner = 'UNIT_RENTAL_DETAIL_PREDICTED_RENT_APPROVED_BY_OWNER',
  UnitUtilityFlatFee = 'UNIT_UTILITY_FLAT_FEE',
  UnitUtilityNotes = 'UNIT_UTILITY_NOTES',
  UnitAccessType = 'UNIT_ACCESS_TYPE',
  UnitAccessNotes = 'UNIT_ACCESS_NOTES',
  UnitGeneralAccessNotes = 'UNIT_GENERAL_ACCESS_NOTES',
  UnitCodeBoxSerial = 'UNIT_CODE_BOX_SERIAL',
  UnitCodeboxLocationDescription = 'UNIT_CODEBOX_LOCATION_DESCRIPTION',
  UnitCodeBoxLocationPhotoFileId = 'UNIT_CODE_BOX_LOCATION_PHOTO_FILE_ID',
  UnitSmartLockProvider = 'UNIT_SMART_LOCK_PROVIDER',
  UnitSmartLockType = 'UNIT_SMART_LOCK_TYPE',
  UnitSmartLockInstallDate = 'UNIT_SMART_LOCK_INSTALL_DATE',
  UnitSmartLockAccessNotes = 'UNIT_SMART_LOCK_ACCESS_NOTES',
  UnitUnitType = 'UNIT_UNIT_TYPE',
  UnitOwnerOccupied = 'UNIT_OWNER_OCCUPIED',
  UnitShortTermRentals = 'UNIT_SHORT_TERM_RENTALS',
  UnitRubsEnabled = 'UNIT_RUBS_ENABLED',
  UnitRubsEnabledOnDate = 'UNIT_RUBS_ENABLED_ON_DATE',
  UnitActive = 'UNIT_ACTIVE',
  UnitArchivedOnDate = 'UNIT_ARCHIVED_ON_DATE',
  UnitSecurityDepositType = 'UNIT_SECURITY_DEPOSIT_TYPE',
  UnitDirectSecureDepositAmount = 'UNIT_DIRECT_SECURE_DEPOSIT_AMOUNT',
  UnitAgentShowingRequired = 'UNIT_AGENT_SHOWING_REQUIRED',
  UnitShowingAssignee = 'UNIT_SHOWING_ASSIGNEE',
  UnitRmNotes = 'UNIT_RM_NOTES',
  UnitAmenitiesDishwasher = 'UNIT_AMENITIES_DISHWASHER',
  UnitAmenitiesWasher = 'UNIT_AMENITIES_WASHER',
  UnitAmenitiesDryer = 'UNIT_AMENITIES_DRYER',
  UnitAmenitiesDryerType = 'UNIT_AMENITIES_DRYER_TYPE',
  UnitAmenitiesGarbageDisposal = 'UNIT_AMENITIES_GARBAGE_DISPOSAL',
  UnitAmenitiesMicrowave = 'UNIT_AMENITIES_MICROWAVE',
  UnitAmenitiesRangeOven = 'UNIT_AMENITIES_RANGE_OVEN',
  UnitAmenitiesRefrigerator = 'UNIT_AMENITIES_REFRIGERATOR',
  UnitAmenitiesTrashCompactor = 'UNIT_AMENITIES_TRASH_COMPACTOR',
  UnitAmenitiesAppliancesComments = 'UNIT_AMENITIES_APPLIANCES_COMMENTS',
  UnitAmenitiesCeilingFan = 'UNIT_AMENITIES_CEILING_FAN',
  UnitAmenitiesAirConditioner = 'UNIT_AMENITIES_AIR_CONDITIONER',
  UnitAmenitiesFireplace = 'UNIT_AMENITIES_FIREPLACE',
  UnitAmenitiesFireplaceType = 'UNIT_AMENITIES_FIREPLACE_TYPE',
  UnitAmenitiesHeatingSystem = 'UNIT_AMENITIES_HEATING_SYSTEM',
  UnitAmenitiesHeatingFuel = 'UNIT_AMENITIES_HEATING_FUEL',
  UnitAmenitiesGarage = 'UNIT_AMENITIES_GARAGE',
  UnitAmenitiesGarageType = 'UNIT_AMENITIES_GARAGE_TYPE',
  UnitAmenitiesBalcony = 'UNIT_AMENITIES_BALCONY',
  UnitAmenitiesAttic = 'UNIT_AMENITIES_ATTIC',
  UnitAmenitiesBasement = 'UNIT_AMENITIES_BASEMENT',
  UnitAmenitiesDisabledAccess = 'UNIT_AMENITIES_DISABLED_ACCESS',
  UnitAmenitiesDoublePaneWindows = 'UNIT_AMENITIES_DOUBLE_PANE_WINDOWS',
  UnitAmenitiesPatio = 'UNIT_AMENITIES_PATIO',
  UnitAmenitiesPorch = 'UNIT_AMENITIES_PORCH',
  UnitAmenitiesPrivateBalcony = 'UNIT_AMENITIES_PRIVATE_BALCONY',
  UnitAmenitiesPrivatePatio = 'UNIT_AMENITIES_PRIVATE_PATIO',
  UnitAmenitiesFurnished = 'UNIT_AMENITIES_FURNISHED',
  UnitAmenitiesLandscapingIncluded = 'UNIT_AMENITIES_LANDSCAPING_INCLUDED',
  UnitAmenitiesOtherAmenities = 'UNIT_AMENITIES_OTHER_AMENITIES',
  UnitAmenitiesLastRenovationDate = 'UNIT_AMENITIES_LAST_RENOVATION_DATE',
  UnitAmenitiesSmartLock = 'UNIT_AMENITIES_SMART_LOCK',
  UnitAmenitiesWdInUnit = 'UNIT_AMENITIES_WD_IN_UNIT',
  UnitAmenitiesWdHookups = 'UNIT_AMENITIES_WD_HOOKUPS',
  UnitAmenitiesSecurityDoor = 'UNIT_AMENITIES_SECURITY_DOOR',
  UnitAmenitiesFloorCovering = 'UNIT_AMENITIES_FLOOR_COVERING',
  UnitUtilitiesElectricPaidBy = 'UNIT_UTILITIES_ELECTRIC_PAID_BY',
  UnitUtilitiesGasPaidBy = 'UNIT_UTILITIES_GAS_PAID_BY',
  UnitUtilitiesWaterPaidBy = 'UNIT_UTILITIES_WATER_PAID_BY',
  UnitUtilitiesSewerPaidBy = 'UNIT_UTILITIES_SEWER_PAID_BY',
  UnitUtilitiesTrashPaidBy = 'UNIT_UTILITIES_TRASH_PAID_BY',
  UnitUtilitiesInternetPaidBy = 'UNIT_UTILITIES_INTERNET_PAID_BY',
  UnitUtilitiesCablePaidBy = 'UNIT_UTILITIES_CABLE_PAID_BY',
  UnitMarketingPostCoBroke = 'CO_BROKE',
  UnitMarketingPostCoBrokeDate = 'CO_BROKE_DATE',
  UnitMarketingPostListedOnDate = 'LISTED_ON_DATE',
  UnitMarketingPostListedOnMlsDate = 'LISTED_ON_MLS_DATE',
  RentIncentive = 'RENT_INCENTIVE',
  FeeIncentive = 'FEE_INCENTIVE',
  DepositIncentive = 'DEPOSIT_INCENTIVE',
  PromotionalIncentive = 'PROMOTIONAL_INCENTIVE',
}

export const myndUnitHistoryTypes = new Map<MyndUnitHistoryType, string>([
  [MyndUnitHistoryType.UnitAssetId, 'Property'],
  [MyndUnitHistoryType.UnitAddress, 'Mailing Address'],
  [MyndUnitHistoryType.UnitNumber, 'Unit Number'],
  [MyndUnitHistoryType.UnitCity, 'City'],
  [MyndUnitHistoryType.UnitState, 'State'],
  [MyndUnitHistoryType.UnitCounty, 'County'],
  [MyndUnitHistoryType.UnitZip, 'ZIP'],
  [MyndUnitHistoryType.UnitFloorPlan, 'Floor Plan'],
  [MyndUnitHistoryType.UnitBedroomsNumber, 'Bedrooms'],
  [MyndUnitHistoryType.UnitFullBathroomsNumber, 'Bathrooms'],
  [MyndUnitHistoryType.UnitHalfBathroomsNumber, 'Half-bathrooms'],
  [MyndUnitHistoryType.UnitSquareFootage, 'Square Footage'],
  [MyndUnitHistoryType.UnitFloor, 'Unit on Floor'],
  [MyndUnitHistoryType.UnitMailboxNumber, 'Mailbox Number'],
  [MyndUnitHistoryType.UnitParkingSpaceType, 'Parking Type'],
  [MyndUnitHistoryType.UnitParkingSpaceNumber, 'Parking Space'],
  [MyndUnitHistoryType.UnitParkingSpaceUncoveredAmount, 'Uncovered Parking Space Amount'],
  [MyndUnitHistoryType.UnitParkingSpaceCoveredAmount, 'Covered Parking Space Amount'],
  [MyndUnitHistoryType.UnitVirtualTourLink, 'Virtual Tour link'],
  [MyndUnitHistoryType.UnitPhotosLink, 'Photos Link'],
  [MyndUnitHistoryType.UnitSmartlockSerial, 'Smart Lock Serial'],
  [MyndUnitHistoryType.UnitOrder, 'Unit Order'],
  [MyndUnitHistoryType.UnitStatus, 'Unit Status'],
  [MyndUnitHistoryType.UnitStatusV2, 'Status'],
  [MyndUnitHistoryType.UnitStatusChangeReason, 'Status Change Reason'],
  [MyndUnitHistoryType.UnitStatusReasonCode, 'Substatus'],
  [MyndUnitHistoryType.UnitOccupied, 'Occupied'],
  [MyndUnitHistoryType.UnitRenovationState, 'Renovation State'],
  [MyndUnitHistoryType.UnitRentalDetailVacantReadyDate, 'Estimated Ready'],
  [MyndUnitHistoryType.UnitRentalDetailActualVacantReadyDate, 'Ready Date'],
  [MyndUnitHistoryType.UnitRentalDetailVacantDownDate, 'Vacant Down'],
  [MyndUnitHistoryType.UnitRentalDetailTargetRentAmount, 'Listed rent'],
  [MyndUnitHistoryType.UnitRentalDetailParkingRent, 'Parking rent'],
  [MyndUnitHistoryType.UnitRentalDetailMinLeaseTermType, 'Min Lease Term'],
  [MyndUnitHistoryType.UnitRentalDetailMarketingDescription, 'Marketing Description'],
  [MyndUnitHistoryType.UnitRentalDetailMarketingHeader, 'Marketing Title'],
  [MyndUnitHistoryType.UnitRentalDetailConcessions, 'Concessions'],
  [MyndUnitHistoryType.UnitRentalDetailFlatFeeAmount, 'Flat Fee Amount'],
  [MyndUnitHistoryType.UnitRentalDetailHotUnit, 'Hot Unit?'],
  [MyndUnitHistoryType.UnitRentalDetailPredictedRentMin, 'Predicted Rent Min'],
  [MyndUnitHistoryType.UnitRentalDetailPredictedRentMax, 'Predicted Rent Max'],
  [MyndUnitHistoryType.UnitRentalDetailPredictedRentApprovedByOwner, 'Predicted Rent Approved by Owner'],
  [MyndUnitHistoryType.UnitUtilityFlatFee, 'Flat Fee?'],
  [MyndUnitHistoryType.UnitUtilityNotes, 'Utilities Notes'],
  [MyndUnitHistoryType.UnitAccessType, 'Access Type'],
  [MyndUnitHistoryType.UnitAccessNotes, 'Internal Access Notes'],
  [MyndUnitHistoryType.UnitGeneralAccessNotes, 'General Tour Access Notes'],
  [MyndUnitHistoryType.UnitCodeBoxSerial, 'CodeBox Serial #'],
  [MyndUnitHistoryType.UnitCodeboxLocationDescription, 'CodeBox Location Description'],
  [MyndUnitHistoryType.UnitCodeBoxLocationPhotoFileId, 'Photo of Location'],
  [MyndUnitHistoryType.UnitSmartLockProvider, 'Smart Lock Provider'],
  [MyndUnitHistoryType.UnitSmartLockType, 'Smart Lock Type'],
  [MyndUnitHistoryType.UnitSmartLockInstallDate, 'Smart Lock Install Date'],
  [MyndUnitHistoryType.UnitSmartLockAccessNotes, 'Smart Lock Access Notes'],
  [MyndUnitHistoryType.UnitUnitType, 'Unit type'],
  [MyndUnitHistoryType.UnitOwnerOccupied, 'Owner Occupied?'],
  [MyndUnitHistoryType.UnitShortTermRentals, 'Short Term Rental?'],
  [MyndUnitHistoryType.UnitRubsEnabled, 'RUBS enabled?'],
  [MyndUnitHistoryType.UnitRubsEnabledOnDate, 'RUBS enabled on date'],
  [MyndUnitHistoryType.UnitActive, 'Active?'],
  [MyndUnitHistoryType.UnitArchivedOnDate, 'Archived on'],
  [MyndUnitHistoryType.UnitSecurityDepositType, 'Security Deposit'],
  [MyndUnitHistoryType.UnitDirectSecureDepositAmount, 'Deposit Amount:'],
  [MyndUnitHistoryType.UnitAgentShowingRequired, 'Agent showing required?'],
  [MyndUnitHistoryType.UnitShowingAssignee, 'Showing Assignee'],
  [MyndUnitHistoryType.UnitRmNotes, 'R&M Notes'],
  [MyndUnitHistoryType.UnitAmenitiesDishwasher, 'Dishwasher?'],
  [MyndUnitHistoryType.UnitAmenitiesWasher, 'Washer?'],
  [MyndUnitHistoryType.UnitAmenitiesDryer, 'Dryer?'],
  [MyndUnitHistoryType.UnitAmenitiesDryerType, 'Dryer Type'],
  [MyndUnitHistoryType.UnitAmenitiesGarbageDisposal, 'Garbage Disposal?'],
  [MyndUnitHistoryType.UnitAmenitiesMicrowave, 'Microwave?'],
  [MyndUnitHistoryType.UnitAmenitiesRangeOven, 'Range Oven'],
  [MyndUnitHistoryType.UnitAmenitiesRefrigerator, 'Refrigerator?'],
  [MyndUnitHistoryType.UnitAmenitiesTrashCompactor, 'Trash Compactor?'],
  [MyndUnitHistoryType.UnitAmenitiesAppliancesComments, 'Appliances Comments'],
  [MyndUnitHistoryType.UnitAmenitiesCeilingFan, 'Ceiling Fan?'],
  [MyndUnitHistoryType.UnitAmenitiesAirConditioner, 'Air Conditioner?'],
  [MyndUnitHistoryType.UnitAmenitiesFireplace, 'Fireplace?'],
  [MyndUnitHistoryType.UnitAmenitiesFireplaceType, 'Fireplace Type'],
  [MyndUnitHistoryType.UnitAmenitiesHeatingSystem, 'Heating system'],
  [MyndUnitHistoryType.UnitAmenitiesHeatingFuel, 'Heating fuel'],
  [MyndUnitHistoryType.UnitAmenitiesGarage, 'Garage?'],
  [MyndUnitHistoryType.UnitAmenitiesGarageType, 'Garage Type'],
  [MyndUnitHistoryType.UnitAmenitiesBalcony, 'Balcony?'],
  [MyndUnitHistoryType.UnitAmenitiesAttic, 'Attic?'],
  [MyndUnitHistoryType.UnitAmenitiesBasement, 'Basement?'],
  [MyndUnitHistoryType.UnitAmenitiesDisabledAccess, 'Disabled Access?'],
  [MyndUnitHistoryType.UnitAmenitiesDoublePaneWindows, 'Double Pane Windows?'],
  [MyndUnitHistoryType.UnitAmenitiesPatio, 'Patio?'],
  [MyndUnitHistoryType.UnitAmenitiesPorch, 'Porch?'],
  [MyndUnitHistoryType.UnitAmenitiesPrivateBalcony, 'Private Balcony?'],
  [MyndUnitHistoryType.UnitAmenitiesPrivatePatio, 'Private Patio?'],
  [MyndUnitHistoryType.UnitAmenitiesFurnished, 'Furnished?'],
  [MyndUnitHistoryType.UnitAmenitiesLandscapingIncluded, 'Landscaping Included?'],
  [MyndUnitHistoryType.UnitAmenitiesOtherAmenities, 'Marketable Features'],
  [MyndUnitHistoryType.UnitAmenitiesLastRenovationDate, 'Last renovation date'],
  [MyndUnitHistoryType.UnitAmenitiesSmartLock, 'Smart Lock?'],
  [MyndUnitHistoryType.UnitAmenitiesWdInUnit, 'Washer/Dryer In Unit?'],
  [MyndUnitHistoryType.UnitAmenitiesWdHookups, 'WD Hookups?'],
  [MyndUnitHistoryType.UnitAmenitiesSecurityDoor, 'Security Door?'],
  [MyndUnitHistoryType.UnitAmenitiesFloorCovering, 'Floor Coverings'],
  [MyndUnitHistoryType.UnitUtilitiesElectricPaidBy, 'Electricity Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesGasPaidBy, 'Gas Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesWaterPaidBy, 'Water Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesSewerPaidBy, 'Sewer Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesTrashPaidBy, 'Trash Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesInternetPaidBy, 'Internet Paid By'],
  [MyndUnitHistoryType.UnitUtilitiesCablePaidBy, 'Cable Paid By'],
  [MyndUnitHistoryType.UnitMarketingPostCoBroke, 'Co-Broke'],
  [MyndUnitHistoryType.UnitMarketingPostCoBrokeDate, 'Co-Broke Date'],
  [MyndUnitHistoryType.UnitMarketingPostListedOnDate, 'Listed on Date'],
  [MyndUnitHistoryType.UnitMarketingPostListedOnMlsDate, 'Listed on MLS Date'],
  [MyndUnitHistoryType.RentIncentive, 'Rent Incentive'],
  [MyndUnitHistoryType.FeeIncentive, 'Fee Incentive'],
  [MyndUnitHistoryType.DepositIncentive, 'Deposit Incentive'],
  [MyndUnitHistoryType.PromotionalIncentive, 'Promotional Incentive'],
]);

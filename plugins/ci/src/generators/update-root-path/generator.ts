import { getProjects, Tree } from '@nx/devkit';
import { readFileSync, writeFileSync } from 'fs';
import * as path from 'path';

import { UpdateRootPathGeneratorSchema } from './schema';

export async function updateRootPathGenerator(tree: Tree, options: UpdateRootPathGeneratorSchema) {
  const allProjects = getProjects(tree);
  const projectConfig = allProjects.get(options.project);

  if (!projectConfig) {
    throw new Error(`Project ${options.project} not found`);
  }

  const dist = projectConfig?.targets?.build?.options?.outputPath?.base;

  if (dist) {
    updateIndexHtml(dist, options.prefix);
    updateLoadEnvJs(dist, options.prefix);
  }
}

function updateIndexHtml(dist: string, prefix: string) {
  const indexHtmlPath = `${dist}/index.html`;
  const indexHtmlContent = readFileSync(indexHtmlPath).toString();
  const baseHrefRe = /<base href="(.*)".*>/;
  const [, baseHref] = indexHtmlContent.match(baseHrefRe);
  const newBaseHref = getNormalizedPath(baseHref, prefix);

  const newIndexHtmlContent = indexHtmlContent.replace(baseHrefRe, `<base href="${newBaseHref}" />`);

  writeFileSync(indexHtmlPath, newIndexHtmlContent);
}

function updateLoadEnvJs(dist: string, prefix: string) {
  const loadEnvJsPath = `${dist}/assets/load-env.js`;
  const loadEnvJsContent = readFileSync(loadEnvJsPath).toString();
  const rootPathRe = /rootPath: \'(.*)\'/;
  const [, rootPath] = loadEnvJsContent.match(rootPathRe);
  const newRootPath = getNormalizedPath(rootPath, prefix);

  const newLoadEnvJsContent = loadEnvJsContent.replace(rootPathRe, `rootPath: '${newRootPath}'`);

  writeFileSync(loadEnvJsPath, newLoadEnvJsContent);
}

function getNormalizedPath(basePath: string, prefix: string) {
  const newPath = ['/', prefix, basePath].join('/');

  return path.normalize(newPath);
}

export default updateRootPathGenerator;

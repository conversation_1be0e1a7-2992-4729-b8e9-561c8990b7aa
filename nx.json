{"$schema": "./node_modules/nx/schemas/nx-schema.json", "workspaceLayout": {"appsDir": "apps", "libsDir": "packages"}, "namedInputs": {"sharedGlobals": ["{workspaceRoot}/configs/**/*"], "default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json", "!{projectRoot}/.stylelintrc(.(json|yml|yaml|js))?"]}, "pluginsConfig": {"@nx/js": {"projectsAffectedByDependencyUpdates": "auto"}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "link": {"dependsOn": ["build"], "cache": false}, "build-storybook": {"inputs": ["default", "^production", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"], "cache": true}, "stylelint": {"inputs": ["default", "{workspaceRoot}/.stylelintrc(.(json|yml|yaml|js))?"], "cache": true}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@angular-devkit/build-angular:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/angular:package": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/angular:ng-packagr-lite": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "generators": {"@nx/angular:application": {"e2eTestRunner": "none", "linter": "eslint", "style": "css", "unitTestRunner": "jest"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest", "style": "scss", "changeDetection": "OnPush", "setParserOptionsProject": true, "displayBlock": true, "standalone": true}, "@nx/angular:component": {"style": "scss"}, "@ngneat/spectator:spectator-component": {"style": "scss", "changeDetection": "OnPush", "unitTestRunner": "jest"}}, "defaultBase": "origin/main", "parallel": 2, "useInferencePlugins": false}
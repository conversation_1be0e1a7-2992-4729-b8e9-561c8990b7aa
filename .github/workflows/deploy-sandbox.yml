name: Deploy sandbox

on:
  workflow_call:
    inputs:
      service-name:
        description: "The service name"
        type: string
        required: true

      project-name:
        description: "The Nx project name"
        type: string
        required: true

      sandbox-prefix:
        description: "The sandbox prefix"
        type: string
        required: true

      s3-bucket-prefix:
        description: "The S3 bucket prefix"
        type: string
        required: true

      base-href:
        description: "The base href"
        type: string
        required: true

      dns-prefix:
        description: "The DNS prefix"
        type: string
        required: true

      dist-path:
        description: "The path to the dist folder"
        type: string
        required: true

env:
  SANDBOX_PREFIX: ${{ inputs.sandbox-prefix }}
  NX_SELF_HOSTED_REMOTE_CACHE_SERVER: http://nx-cache.roofstock.ws/repo/${{ github.event.repository.name }}
  NX_SELF_HOSTED_REMOTE_CACHE_ACCESS_TOKEN: ${{ secrets.NX_CACHE_ACCESS_TOKEN }}
  NODE_TLS_REJECT_UNAUTHORIZED: 0

jobs:
  deploy-sandbox:
    runs-on: otto-common-runners
    name: ${{ matrix.environment }}

    strategy:
      fail-fast: false
      matrix:
        environment: [dev, staging]

    env:
      S3_BUCKET: s3://${{ inputs.s3-bucket-prefix }}-${{ matrix.environment }}-mynd
      AWS_PROFILE: ${{ matrix.environment }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Generate Git Token
        id: rs-actions-builder
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.RS_GIT_DISTRIBUTOR_APP_ID }}
          private_key: ${{ secrets.RS_GIT_DISTRIBUTOR_PRIV_KEY }}

      - name: Checkout Code otto-deployment-tools
        uses: actions/checkout@v4
        with:
          repository: 'roofstock/otto-deployment-tools'
          token: ${{ steps.rs-actions-builder.outputs.token }}
          path: otto-deployment-tools

      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'
          cache: 'pip'

      - name: Install python dependencies
        run: pip install --upgrade -r otto-deployment-tools/requirements.txt

      - name: Checkout Code otto-ecs-services
        uses: actions/checkout@v4
        with:
          repository: 'roofstock/otto-ecs-services'
          token: ${{ steps.rs-actions-builder.outputs.token }}
          path: otto-ecs-services

      - name: Init AWS ${{ matrix.environment }} access
        run: |
          mkdir -p ~/.aws
          cp ./otto-deployment-tools/.github/scripts/.aws_config ~/.aws/config
          cp ./otto-deployment-tools/.github/scripts/eks-credential-processrole.sh /home/<USER>/eks-credential-processrole.sh

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Build
        run: npx nx build ${{ inputs.project-name }}

      - name: Process load-env.js and index.html
        env:
          LOAD_ENV_JS_TPL_PATH: "../${{ inputs.dist-path }}/assets/load-env.js.tpl"
          SERVICE_NAME: ${{ inputs.service-name }}
          ENV_NAME: ${{ matrix.environment }}
        run: |
          cd otto-ecs-services

          # generate load-env.js based on load-env.js.tpl
          ../otto-deployment-tools/ecs/update_service.py parse_website_configs -sn $SERVICE_NAME -sbx $ENV_NAME --tpl $LOAD_ENV_JS_TPL_PATH

          cd ..

          # replace base path in index.html and rootPath with a preview env path in load-env.js
          npx nx g @myndmanagement/ci:update-root-path --project=${{ inputs.project-name }} --prefix=${{ env.SANDBOX_PREFIX }}

      - name: Deploy to S3
        run: aws s3 sync "${{ inputs.dist-path }}" "${{ env.S3_BUCKET }}/${{ env.SANDBOX_PREFIX }}" --delete

name: Deploy back-office sandbox

on:
  workflow_call:
    inputs:
      deploy-configs:
        description: "The deployment configuration"
        type: string
        required: true

      sandbox-prefix:
        description: "The sandbox prefix"
        type: string
        required: true

env:
  SANDBOX_PREFIX: ${{ inputs.sandbox-prefix }}
  WEBSITE_DNS_PREFIX: "otto-new"
  NX_SELF_HOSTED_REMOTE_CACHE_SERVER: http://nx-cache.roofstock.ws/repo/${{ github.event.repository.name }}
  NX_SELF_HOSTED_REMOTE_CACHE_ACCESS_TOKEN: ${{ secrets.NX_CACHE_ACCESS_TOKEN }}
  NODE_TLS_REJECT_UNAUTHORIZED: 0

jobs:
  deploy-back-office-sandbox:
    runs-on: otto-common-runners
    name: ${{ matrix.environment }}

    strategy:
      fail-fast: false
      matrix:
        environment: [dev, staging]

    env:
      S3_BUCKET: s3://otto-website-${{ matrix.environment }}-mynd
      AWS_PROFILE: ${{ matrix.environment }}
      TMP_FOLDER: "./tmp_otto"

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Generate Git Token
        id: rs-actions-builder
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.RS_GIT_DISTRIBUTOR_APP_ID }}
          private_key: ${{ secrets.RS_GIT_DISTRIBUTOR_PRIV_KEY }}

      - name: Checkout Code otto-deployment-tools
        uses: actions/checkout@v4
        with:
          repository: 'roofstock/otto-deployment-tools'
          token: ${{ steps.rs-actions-builder.outputs.token }}
          path: otto-deployment-tools

      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'
          cache: 'pip'

      - name: Install python dependencies
        run: pip install --upgrade -r otto-deployment-tools/requirements.txt

      - name: Checkout Code otto-ecs-services
        uses: actions/checkout@v4
        with:
          repository: 'roofstock/otto-ecs-services'
          token: ${{ steps.rs-actions-builder.outputs.token }}
          path: otto-ecs-services

      - name: Init AWS ${{ matrix.environment }} access
        run: |
          mkdir -p ~/.aws
          cp ./otto-deployment-tools/.github/scripts/.aws_config ~/.aws/config
          cp ./otto-deployment-tools/.github/scripts/eks-credential-processrole.sh /home/<USER>/eks-credential-processrole.sh

      - name: Setup node and install dependencies
        uses: ./.github/actions/setup-node

      - name: Build
        run: npx nx run-many -t build --projects=${{ join(fromJson(inputs.deploy-configs).*.projectName, ',') }}

      - name: Process load-env.js and index.html
        env:
          DEPLOY_CONFIGS: ${{ inputs.deploy-configs }}
        run: |
          mkdir $TMP_FOLDER

          items=$(echo $DEPLOY_CONFIGS | jq -c -r '.[]')

          for item in ${items[@]}; do
            DIST_PATH=$(echo $item | jq -r '.distPath')
            PROJECT_NAME=$(echo $item | jq -r '.projectName')
            SERVICE_NAME=$(echo $item | jq -r '.config.serviceName')
            WEBSITE_PATH_PREFIX=$(echo $item | jq -r '.config."path-prefix"')

            cd otto-ecs-services

            LOAD_ENV_JS_TPL_PATH="../${DIST_PATH}/assets/load-env.js.tpl"

            # generate load-env.js based on load-env.js.tpl
            ../otto-deployment-tools/ecs/update_service.py parse_website_configs -sn $SERVICE_NAME -sbx ${{ matrix.environment }} --tpl $LOAD_ENV_JS_TPL_PATH

            cd ..

            # replace base path in index.html and rootPath with a preview env path in load-env.js
            npx nx g @myndmanagement/ci:update-root-path --project=$PROJECT_NAME --prefix=${{ env.SANDBOX_PREFIX }}

            # update project source in tmp folder
            if [ "$WEBSITE_PATH_PREFIX" != "/" ]; then
              cp -r "${DIST_PATH}/" "${TMP_FOLDER}${WEBSITE_PATH_PREFIX}"
            else
              cp -r "${DIST_PATH}/." "${TMP_FOLDER}"
            fi
          done

      - name: Deploy to S3
        run: aws s3 sync "$TMP_FOLDER" "${{ env.S3_BUCKET }}/${{ env.SANDBOX_PREFIX }}" --delete

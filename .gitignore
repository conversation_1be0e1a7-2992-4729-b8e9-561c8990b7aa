# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist/
/tmp
/out-tsc

# dependencies
**/*/package-lock.json
node_modules
npm-debug.log
npm-tree.html

# IDEs and editors
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/extensions.json
.vscode/launch.json
!.vscode/settings.json
.history/*

# misc
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

# Documentation
/docs
.storybook/documentation.json
typedoc.json

# GitLab CI dynamic assets
affected-unit-tests-pipeline.yml
nx-affected.txt

# Nx
.nx/cache
.nx/workspace-data
./migrations.json

# NgDoc files
.ng-doc
/ng-doc

# Angular
.angular

# Capacitor
**/capacitor.config.json

.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md

# Copilot instructions
.github/copilot-instructions.md
AGENTS.md

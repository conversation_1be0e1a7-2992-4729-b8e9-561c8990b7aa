import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { MyndActionsModule } from '@myndmanagement/actions';
import { MyndAddressComponent } from '@myndmanagement/address';
import { MyndAttachmentsModule } from '@myndmanagement/attachments';
import { MyndCommonModule } from '@myndmanagement/common';
import { MyndCommonOttoModule } from '@myndmanagement/common-otto';
import { MyndCommunicationModule } from '@myndmanagement/communication';
import { MyndCaseChatStore } from '@myndmanagement/communication/cases';
import { MyndFilesCoreModule } from '@myndmanagement/file-manager/core';
import { MyndFileUploaderModule } from '@myndmanagement/file-manager/uploader';
import { MyndFileViewerModule } from '@myndmanagement/file-manager/viewer';
import { MyndFilterableViewModule } from '@myndmanagement/filterable-view';
import { MyndFormsOttoModule } from '@myndmanagement/forms-otto';
import { MyndHistoryModule } from '@myndmanagement/history';
import { MyndFeatureOffLdDirective, MyndFeatureOnLdDirective } from '@myndmanagement/launch-darkly';
import { MyndLayoutModule } from '@myndmanagement/layout';
import {
  MyndLeaseBenefitsPanelComponent,
  MyndLeaseRenewalEvaluationPanelComponent,
  MyndLeasesModule,
} from '@myndmanagement/leases';
import { MyndRelatedActionsModule } from '@myndmanagement/related-actions';
import { MyndRelatedCasesByUnitComponent } from '@myndmanagement/related-cases';
import { MyndSubtasksCoreModule, MyndSubtasksCoreStoreModule } from '@myndmanagement/subtasks-core';
import { MyndSubtasksModule } from '@myndmanagement/subtasks-ui';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { MyndModalsModule } from '@myndmanagement/ui-modals';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { MyndTooltipModule } from '@myndmanagement/ui-tooltip';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';

import { SharedModule } from '../shared/shared.module';

import * as fromComponents from './components';
import { LeaseExpirationHistoryComponent } from './components/lease-expiration-history/lease-expiration-history.component';
import { LeaseExpirationUtilitiesComponent } from './components/lease-utilities/lease-utilities.component';
import { LeaseExpirationCaseMessagesStore } from './services/lease-expiration-case-messages.store';
import { LeaseExpirationService } from './services/lease-expiration.service';
import { LeaseService } from './services/lease.service';
import { RenewalService } from './services/renewal.service';
import { LeaseExpirationEffects } from './store/effects/lease-expiration.effects';
import { LeaseFeesEffects } from './store/effects/lease-fees.effects';
import { UnitEffects } from './store/effects/unit.effects';
import * as fromLeaseExpiration from './store/reducers/lease-expiration.reducer';

const routes: Routes = [
  {
    path: 'lease-expirations/:expirationId',
    component: fromComponents.LeaseExpirationComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'details',
      },
      {
        path: 'details',
        component: fromComponents.LeaseExpirationDetailsComponent,
      },
      {
        path: 'notes',
        component: fromComponents.LeaseExpirationChatComponent,
      },
      {
        path: 'case-messages',
        loadComponent: () => import('./components/lease-expiration-case-messages/lease-expiration-case-messages.component')
          .then(m => m.LeaseExpirationCaseMessagesComponent),
      },
    ],
  },
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,

    RouterModule.forChild(routes),
    StoreModule.forFeature('leaseExpiration', fromLeaseExpiration.leaseExpirationReducer),
    EffectsModule.forFeature([LeaseExpirationEffects, UnitEffects, LeaseFeesEffects]),

    MyndLeasesModule,
    SharedModule,
    MyndFileViewerModule,
    MyndFilesCoreModule,
    MyndFileUploaderModule,

    MyndActionsModule,
    MyndHistoryModule,
    MyndAttachmentsModule,
    MyndCommonModule,
    MyndCommonOttoModule,
    MyndCommunicationModule,
    MyndRelatedActionsModule,
    MyndFilterableViewModule,
    MyndFormsModule,
    MyndFormsOttoModule,
    MyndLayoutModule,
    MyndModalsModule,
    MyndSubtasksModule,
    MyndSubtasksCoreModule,
    MyndSubtasksCoreStoreModule,
    MyndTableModule,
    MyndTooltipModule,
    MyndRelatedCasesByUnitComponent,
    MyndLeaseRenewalEvaluationPanelComponent,
    LeaseExpirationUtilitiesComponent,
    LeaseExpirationHistoryComponent,
    MyndLeaseBenefitsPanelComponent,
    MyndAddressComponent,
    MyndFeatureOffLdDirective,
    MyndFeatureOnLdDirective,
  ],
  declarations: [
    fromComponents.LeaseExpirationComponent,
    fromComponents.LeaseExpirationDetailsComponent,
    fromComponents.LeaseExpirationDetailsInfoComponent,
    fromComponents.LeaseExpirationSidePanelComponent,
    fromComponents.LeaseExpirationChatComponent,
    fromComponents.TabsComponent,
    fromComponents.PersonCardComponent,
    fromComponents.ProposalTemplateSelectModalComponent,
    fromComponents.ProposalTemplatePreviewModalComponent,
    fromComponents.ProposalAcceptModalComponent,
    fromComponents.DeferRenewalModalComponent,
    fromComponents.RenewalPricingComponent,
    fromComponents.RenewalPricingItemComponent,
    fromComponents.RenewalDetailsComponent,
    fromComponents.UnitUtilitiesComponent,
    fromComponents.RelatedServiceRequestsComponent,
  ],
  exports: [fromComponents.LeaseExpirationComponent],
  providers: [
    LeaseService,
    LeaseExpirationService,
    RenewalService,
    MyndCaseChatStore,
    LeaseExpirationCaseMessagesStore,
  ],
})
export class LeaseExpirationModule {}

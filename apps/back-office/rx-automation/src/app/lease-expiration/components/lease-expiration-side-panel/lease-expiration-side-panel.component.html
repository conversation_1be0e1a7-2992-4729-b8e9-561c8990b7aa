<m-side-panel>
  @let leaseExpiration = leaseExpiration$ | async;
  @if (leaseExpiration) {
    <m-side-panel-block header="Admin Info">
      <div class="row status-row">
        <div class="status">
          <div class="status-title">Status</div>
          @if (isStatusChanging$ | async) {
            <m-throbber />
          }
          <div class="status-value">{{ convertStatus(leaseExpiration.status) }}</div>
        </div>
        <m-button
          class="send-proposal-button"
          color="green"
          icon="envelope-sm"
          [isDisabled]="!canSendProposal()"
          (click)="canSendProposal() && openProposalTemplateSelect()"
        >Send Proposals</m-button>
      </div>
      <div class="row on-hold-date-row">
        @if (form.get('status').value === leaseExpirationStatus.OnHold) {
          <span>Revisit Date: {{ expirationRevisitDate$ | async }}</span>
        }
      </div>
      <div class="row">
        <div class="links">
          <div class="link"><a target="_blank" [href]="leaseExpiration.leaseId | mLeaseUrl">View lease &rarr;</a></div>
          <div class="link">
            @if (leaseExpiration.leaseCreationObjectId) {
              <a
                target="_blank"
                [routerLink]="['/lease-creations', leaseExpiration.leaseCreationObjectId]"
              >View lease renewal &rarr;</a>
            }
          </div>
          <div class="link">
            @if (leaseExpiration.caseId) {
              <a target="_blank" [href]="getLinkToCase(leaseExpiration)">View case &rarr;</a>
            }
          </div>
          <div class="link">
            @let unitId = unitId$ | async;
            @if (unitId) {
              <a target="_blank" [href]="residentTrackerUrl + '?Unit-Id=' + unitId">Resident History Tracker &rarr;</a>
            }
          </div>
        </div>
      </div>
      <form class="column" [formGroup]="form" novalidate>
        <div class="form-row double">
          @let control = form.get('expirationType');
          <ng-container *mFeatureOffLd="hideRentIncreaseOptionFf">
            @if (canChangeType(form.get('status').value)) {
              <m-select
                formControlName="expirationType"
                label="Type"
                required="true"
                [error]="control.invalid && control.dirty ? 'Type is required.' : null"
                [selectedItemDisplayText]="control.value ? convertExpirationType(control.value) : 'Select type'"
              >
                @for (type of expirationTypes; track type) {
                  <m-option [value]="type.key">{{ type.value }}</m-option>
                }
              </m-select>
            } @else {
              <m-form-read-only label="Type">{{ convertExpirationType(control.value) }}</m-form-read-only>
            }
          </ng-container>

          <ng-container *mFeatureOnLd="hideRentIncreaseOptionFf">
            <m-form-read-only label="Type">{{ convertExpirationType(control.value) }}</m-form-read-only>
          </ng-container>
        </div>
        @let groups = groups$ | async;
        @if (groups.length) {
          <div class="form-row assigned-user-group">
            <m-assignee-select
              formControlName="assignedBoUserGroup"
              label="Assignee"
              [groups]="groups"
              [disabled]="form.value.status === leaseExpirationStatus.Archived"
            />
          </div>
        }
      </form>
    </m-side-panel-block>
  }
  @if (showCopyingThrobber$ | async) {
    <m-throbber />
  } @else {
    @if (leaseExpiration) {
      <m-subtasks
        [isInitiallyCollapsed]="true"
        [action]="leaseExpiration"
        [hooksConfig]="leSubtasksHooksConfig"
      />
    }
  }
  @if (leaseExpiration) {
    <le-related-service-requests />
    <m-related-cases-by-unit [unit]="unit$ | async" />
  }
  <div class="flex history-block"><a class="history" (click)="showHistory()">History</a></div>

  @if (showSendToCaseButton()) {
    <m-side-panel-block [isFooter]="true">
      <m-button
        color="green"
        [hasMinWidth]="true"
        (click)="sendMessageToCase()"
      >Send message to Case</m-button>
    </m-side-panel-block>
  }

  @let hasReply = hasReply$ | async;
  @let isSaving = isSaving$ | async;
  @if ((form.dirty && form.valid) || hasReply) {
    <m-side-panel-block [isFooter]="true">
      <m-button
        color="green"
        [hasMinWidth]="true"
        [isDisabled]="isSaving"
        [loading]="isSaving"
        (click)="saveLeaseExpiration()"
      >{{ hasReply ? 'Send & Save' : 'Save' }}</m-button>
    </m-side-panel-block>
  }
</m-side-panel>

@if (isHistoryOpen$ | async) {
  <m-side-panel
    header="Lease Expiration History"
    headerType="subpanel"
    [isFixed]="true"
    (close)="hideHistory()"
  >
    <lease-expiration-history />
  </m-side-panel>
}
<proposal-template-select-modal />
<proposal-template-preview-modal />

import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MyndReply } from '@myndmanagement/api-communications';
import { myndConvertLeaseExpirationType, myndExpirationTypes, MyndLeaseService } from '@myndmanagement/api-leases';
import { MyndUnsubscriber } from '@myndmanagement/common';
import { myndOttoAppPrefix } from '@myndmanagement/common-otto';
import { myndFilterTruthy } from '@myndmanagement/common-utils';
import { myndSelectHasReply } from '@myndmanagement/communication';
import { MyndCaseChatStore } from '@myndmanagement/communication/cases';
import { IMyndOption } from '@myndmanagement/forms-utils';
import { MyndPusherService } from '@myndmanagement/pusher';
import {
  IMyndBeforeSubtaskCompleteParam,
  IMyndSubtasksHooksConfig,
  myndSubtasksActions,
} from '@myndmanagement/subtasks-core';
import { MyndModalService } from '@myndmanagement/ui-modals';
import { myndSelectUserGroups } from '@myndmanagement/users';
import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of, switchMap, take } from 'rxjs';
import { distinctUntilChanged, filter, first, map, skip, takeUntil, tap } from 'rxjs/operators';

import { IAppState } from '../../../root/interfaces/app-state.interface';
import {
  convertLeaseExpirationStatus,
  LeaseExpirationStatus,
} from '../../../shared/constants/lease-expiration-statuses';
import { leaseExpirationChannel } from '../../constants/pusher.constant';
import { ILeaseExpiration } from '../../interfaces/lease-expiration.interface';
import { LeaseExpirationCaseMessagesStore } from '../../services/lease-expiration-case-messages.store';
import {
  hideHistory,
  loadLeaseExpiration,
  openProposalTemplateSelectionModal,
  showHistory,
  updateLeaseExpiration,
} from '../../store/actions/lease-expiration.actions';
import { IProposalsAndParticipantsCount } from '../../store/lease-expiration.state';
import { LeaseExpirationSelectors } from '../../store/selectors/lease-expiration.selectors';

interface ILeaseExpStatusChangeEvent {
  oldStatus: LeaseExpirationStatus;
  newStatus: LeaseExpirationStatus;
}

export const expirationStatusesAllowingChangingTypeOrSendingForApproval: LeaseExpirationStatus[] = [
  LeaseExpirationStatus.Upcoming,
  LeaseExpirationStatus.InProgress,
  LeaseExpirationStatus.PendingMarketAnalysis,
  LeaseExpirationStatus.PendingProposalCreation,
  LeaseExpirationStatus.PmReview,
  LeaseExpirationStatus.PendingInvestorApproval,
];

@Component({
  selector: 'lease-expiration-side-panel',
  templateUrl: './lease-expiration-side-panel.component.html',
  styleUrls: ['./lease-expiration-side-panel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class LeaseExpirationSidePanelComponent extends MyndUnsubscriber implements OnInit {
  private readonly actions$ = inject(Actions);
  private readonly formBuilder = inject(UntypedFormBuilder);
  private readonly store = inject<Store<IAppState>>(Store);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly pusherService = inject(MyndPusherService);
  private readonly caseChatStore = inject(MyndCaseChatStore);
  private readonly leCaseMessagesStore = inject(LeaseExpirationCaseMessagesStore);
  private readonly modalService = inject(MyndModalService);
  private readonly myndLeaseService = inject(MyndLeaseService);

  groups$ = this.store.select(myndSelectUserGroups);
  leaseExpiration$ = this.store.select('leaseExpiration', 'leaseExpiration');
  leaseExpirationStatus$ = this.store.select('leaseExpiration', 'leaseExpiration', 'status');
  leaseDetails$ = this.store.select('leaseExpiration', 'leaseDetails');
  isSaving$ = this.store.select('leaseExpiration', 'isSaving');
  isChangingEscalatedState$ = this.store.select('leaseExpiration', 'isChangingEscalatedState');
  hasReply$ = this.store.select(myndSelectHasReply);
  expirationRevisitDate$ = this.store.select('leaseExpiration', 'leaseExpiration', 'revisitDate');
  unit$ = this.store.select('leaseExpiration', 'leaseUnit');
  unitId$ = this.store.select('leaseExpiration', 'leaseUnit', 'unitId');

  proposalsAndParticipantsCount$ = this.store.select(
    LeaseExpirationSelectors.selectCounts,
  );

  isEditingMode$ = this.store.select('leaseExpiration', 'isEditingMode');
  isStatusChanging$ = this.store.select('leaseExpiration', 'isStatusChanging');
  isHistoryOpen$ = this.store.select('leaseExpiration', 'isHistoryOpen');
  isLoadingExpiration$ = this.store.select(LeaseExpirationSelectors.isLoadingExpiration);
  showCopyingThrobber$ = this.store.select(LeaseExpirationSelectors.isCopyingExpiration).pipe(
    switchMap((isCopying) => {
      if (isCopying) {
        this.store.dispatch(myndSubtasksActions.resetState());
        return of(true);
      }
      return this.leaseExpiration$.pipe(
        skip(1), // skip stored LE and wait for the new copied
        map(() => false),
      );
    }),
  );

  leaseFlow = this.store.selectSignal(LeaseExpirationSelectors.getLeaseFlow);
  unitUtilities = this.store.selectSignal(LeaseExpirationSelectors.getUnitUtilities);

  leSubtasksHooksConfig: IMyndSubtasksHooksConfig = {
    beforeSubtaskComplete: (params: IMyndBeforeSubtaskCompleteParam): Promise<{ abort: boolean }> => {
      if (params.step?.type === 'send_Proposals') {
        return this.beforeSendProposalStepCompletedHook();
      }
      return Promise.resolve({ abort: false });
    },
  };

  convertStatus = convertLeaseExpirationStatus;
  convertExpirationType = myndConvertLeaseExpirationType;

  expirationTypes: IMyndOption[] = myndExpirationTypes;
  readonly leaseExpirationStatus = LeaseExpirationStatus;

  counters: IProposalsAndParticipantsCount;
  form: UntypedFormGroup = this.formBuilder.group({
    assignedBoUserGroup: [
      {
        assignedBoUserGroupId: '',
        assignedBoUserId: '',
      },
      Validators.required,
    ],
    status: '',
    expirationType: '',
  });
  isEditingMode: boolean;

  readonly residentTrackerUrl = 'https://app.sigmacomputing.com/mynd/workbook/Resident-History-Tracker-1by6D6BJQypoIREYNtzJDd/tag/DS-PROD';
  readonly hideRentIncreaseOptionFf = 'les-6372-hide-rent-increase-le-type';

  allowedStatuses: LeaseExpirationStatus[] = expirationStatusesAllowingChangingTypeOrSendingForApproval;

  allowedSendProposalsStatuses: string[] = [
    LeaseExpirationStatus.InProgress,
    LeaseExpirationStatus.Pending,
    LeaseExpirationStatus.Accepted,
    LeaseExpirationStatus.ProposalApprovedNotSent,
    LeaseExpirationStatus.PendingProposalAcceptance,
    LeaseExpirationStatus.ProposalAcceptedPendingSignatures,
  ];

  caseReply = computed(() => new MyndReply(this.caseChatStore.reply()));
  showSendToCaseButton = computed(() => {
    return this.caseReply()?.hasReply();
  });

  ngOnInit(): void {
    this.leaseExpiration$
      .pipe(
        filter((leaseExpiration: ILeaseExpiration) => Boolean(leaseExpiration)),
        takeUntil(this.unsubscribe),
      )
      .subscribe((leaseExpiration) => {
        this.form.patchValue({
          assignedBoUserGroup: {
            assignedBoUserGroupId: leaseExpiration.assignedBoUserGroupId,
            assignedBoUserId: leaseExpiration.assignedBoUserId,
          },
          status: leaseExpiration.status,
          expirationType: leaseExpiration.expirationType,
        });
        this.cdr.markForCheck();
      });

    this.proposalsAndParticipantsCount$
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((proposalsAndParticipantsCount) => {
        this.counters = proposalsAndParticipantsCount;
        this.cdr.markForCheck();
      });

    this.isEditingMode$
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((isEditingMode) => {
        this.isEditingMode = isEditingMode;
        this.cdr.markForCheck();
      });

    this.listenStatusChangeFromPusher();
  }

  sendMessageToCase(): void {
    this.leCaseMessagesStore.sendMessageToCase();
  }

  saveLeaseExpiration(): void {
    this.leaseExpiration$
      .pipe(first())
      .subscribe((leaseExpiration: ILeaseExpiration) => {
        this.store.dispatch(updateLeaseExpiration({
          expirationUpdate: {
            expirationId: leaseExpiration.expirationId,
            expirationType: this.form.value.expirationType,
            status: this.form.value.status,
            escalated: this.form.value.escalated,
            assignedBoUserGroupId: this.form.value.assignedBoUserGroup?.assignedBoUserGroupId || null,
            assignedBoUserId: this.form.value.assignedBoUserGroup?.assignedBoUserId || null,
          },
        }));

        this.form.markAsPristine();
        this.cdr.markForCheck();
      });
  }

  getLinkToCase(leaseExpiration: ILeaseExpiration): string {
    return `${myndOttoAppPrefix.caseManagement}/${leaseExpiration.caseId}`;
  }

  openProposalTemplateSelect(): void {
    this.store.dispatch(openProposalTemplateSelectionModal());
  }

  canSendProposal(): boolean {
    return this.counters?.proposalsCount && this.allowedSendProposalsStatuses.includes(this.form.get('status').value);
  }

  canChangeType(status: LeaseExpirationStatus): boolean {
    return this.allowedStatuses.includes(status) || status === LeaseExpirationStatus.Cancelled;
  }

  showHistory(): void {
    this.store.dispatch(showHistory());
  }

  hideHistory(): void {
    this.store.dispatch(hideHistory());
  }

  private listenStatusChangeFromPusher(): void {
    const manualSubtaskCompletion$ = this.actions$.pipe(
      ofType(myndSubtasksActions.completeSubtaskWithUserInputs, myndSubtasksActions.completeSubtask),
      take(1),
    );

    manualSubtaskCompletion$.pipe(
      switchMap(() => this.leaseExpiration$),
      map(leaseExpiration => leaseExpiration?.expirationId),
      myndFilterTruthy(),
      distinctUntilChanged(),
      switchMap(expirationId => this.pusherService.listen(
        'LEASE_EXPIRATION_STATUS_CHANGED',
        leaseExpirationChannel(expirationId),
      ).pipe(
        map((eventStr) => JSON.parse(eventStr as string) as ILeaseExpStatusChangeEvent),
        tap((event: ILeaseExpStatusChangeEvent) => {
          if (event.newStatus) {
            this.store.dispatch(loadLeaseExpiration({ expirationId }));
          }
        }),
      )),
      takeUntil(this.unsubscribe),
    ).subscribe();
  }

  private beforeSendProposalStepCompletedHook(): Promise<{ abort: boolean }> {
    return this.comparePaymentResponsibilityBetweenLeaseAndUnit().then(({ equals }) => {
      if (!equals) {
        return this.modalService.confirm({
          title: 'Are you sure?',
          description: 'There are differences in Utility Responsibility. Have you accounted for them?' },
        )
          .then(confirmed => {
            return { abort: !confirmed };
          });
      }
      return { abort: false };
    });
  }

  private comparePaymentResponsibilityBetweenLeaseAndUnit(): Promise<{ equals: boolean }> {
    const currentLeaseContractId = this.leaseFlow().currentLeaseContractId;
    if (!currentLeaseContractId) {
      return Promise.resolve({ equals: true });
    }
    return this.myndLeaseService.getContract(currentLeaseContractId).pipe(
      map(leaseContract => {
        const leaseResponsibilities = leaseContract.utilities.paymentResponsibilities;
        const unitUtilities = this.unitUtilities();

        const equals = leaseResponsibilities.cablePaidBy === unitUtilities.cablePaidBy
          && leaseResponsibilities.electricPaidBy === unitUtilities.electricPaidBy
          && leaseResponsibilities.gasPaidBy === unitUtilities.gasPaidBy
          && leaseResponsibilities.internetPaidBy === unitUtilities.internetPaidBy
          && leaseResponsibilities.propanePaidBy === unitUtilities.propanePaidBy
          && leaseResponsibilities.sewerPaidBy === unitUtilities.sewerPaidBy
          && leaseResponsibilities.solarPaidBy === unitUtilities.solarPaidBy
          && leaseResponsibilities.trashPaidBy === unitUtilities.trashPaidBy
          && leaseResponsibilities.waterPaidBy === unitUtilities.waterPaidBy;

        return { equals };
      }),
    ).toPromise();
  }
}

<div class="wrapper" *mLet="componentStore.vm$ | async as vm">
  <div class="header">
    <div class="title">Lease and Addenda</div>
    <div class="controls">
      <m-button
        tooltip="This lease has been sent. You can't edit it unless you go to Actions > Edit and Resend"
        tooltipPlacement="left"
        tooltipShiftVerticaly="-70"
        tooltipShiftHorizontaly="-10"
        [tooltipDisabled]="vm.leaseStatus === MyndLeaseCreationObjectStatus.New"
        [isDisabled]="vm.isLoading || vm.leaseStatus !== MyndLeaseCreationObjectStatus.New"
        [color]="MyndButtonColor.White"
        [hasMinWidth]="false"
        [small]="true"
        (click)="saveAddendum()"
      >SAVE</m-button>

      <m-button
        icon="pencil-sm"
        tooltip="This lease has been sent. You can't edit it unless you go to Actions > Edit and Resend"
        tooltipPlacement="left"
        tooltipShiftVerticaly="-70"
        tooltipShiftHorizontaly="-10"
        [tooltipDisabled]="vm.leaseStatus === MyndLeaseCreationObjectStatus.New"
        [isDisabled]="vm.leaseStatus !== MyndLeaseCreationObjectStatus.New"
        [color]="MyndButtonColor.White"
        [hasMinWidth]="false"
        [small]="true"
        (click)="editAddendum()"
      >EDIT</m-button>
    </div>
  </div>

  <div class="main" *ngIf="vm.leaseUnit && vm.addendaList">
    <div class="lease">
      <span>{{ vm.addendaList.leaseType | mGetOptionValue: leaseTypes }} Lease</span>
    </div>

    <form
      [formGroup]="componentFormProvider.addendaForm"
      *ngIf="vm.addendaList.leaseType !== LeaseType.Storage && vm.addendaList.leaseType !== LeaseType.Parking"
    >
      <ng-container *mLet="componentStore.addendaDetailsVisibility$ | async as addendaDetailsVisible">
        <key-addenda></key-addenda>
        <pet-addenda *ngIf="vm.addendaList.animalAddendum"></pet-addenda>
        <parking-addenda *ngIf="vm.addendaList.parkingSpaceAddendum"></parking-addenda>
        <storage-addenda *ngIf="vm.addendaList.storageAddendum"></storage-addenda>
        <concession-addenda [proposal]="proposal()" *ngIf="vm.addendaList.concessionAddendum"></concession-addenda>
        <universal-addenda
          *ngFor="
            let addenda of componentStore.universalAddendaSelected$ | async | keyvalue;
            trackBy: componentFormProvider.trackByItemKey
          "
          [addendaName]="addenda.value.value"
          [isVisible]="vm.addendaList[addenda.key]"
        ></universal-addenda>
      </ng-container>
    </form>
  </div>

  <m-throbber *ngIf="!vm.leaseUnit || !vm.addendaList" size="large"></m-throbber>
</div>

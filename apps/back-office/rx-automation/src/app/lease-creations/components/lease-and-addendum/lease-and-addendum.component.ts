import { ChangeDetectionStrategy, Component, EventEmitter, input, OnInit, Output } from '@angular/core';
import { IMyndProposal, MyndLeaseCreationObjectStatus } from '@myndmanagement/api-leases';
import { MyndUnsubscribeService } from '@myndmanagement/common';
import { myndFilterTruthy } from '@myndmanagement/common-utils';
import { MyndButtonColor } from '@myndmanagement/forms-utils';
import { filter, takeUntil } from 'rxjs/operators';

import { LeaseType, leaseTypes } from '../../constants/lease-types.constant';
import { IAddendaAttributes, IAddendaList } from '../../interfaces';

import { LeaseAndAddendumComponentForm } from './lease-and-addendum.component.form';
import { LeaseAndAddendumComponentStore } from './lease-and-addendum.component.store';

@Component({
  selector: 'lease-and-addendum',
  templateUrl: './lease-and-addendum.component.html',
  styleUrls: ['./lease-and-addendum.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    MyndUnsubscribeService,
    LeaseAndAddendumComponentStore,
    LeaseAndAddendumComponentForm,
  ],
  standalone: false,
})
export class LeaseAndAddendumComponent implements OnInit {

  proposal = input<IMyndProposal>();
  @Output() openAddendumSidePanel: EventEmitter<void> = new EventEmitter<void>();

  readonly leaseTypes = leaseTypes;
  readonly MyndLeaseCreationObjectStatus = MyndLeaseCreationObjectStatus;
  readonly MyndButtonColor = MyndButtonColor;
  readonly LeaseType = LeaseType;

  constructor(
    private unsubscribe: MyndUnsubscribeService,
    public componentStore: LeaseAndAddendumComponentStore,
    public componentFormProvider: LeaseAndAddendumComponentForm,
  ) {}

  ngOnInit(): void {
    this.componentStore.addendaList$.pipe(
      myndFilterTruthy(),
      takeUntil(this.unsubscribe),
    ).subscribe((addendaList: IAddendaList) => {
      this.componentStore.setUniversalAddendum(addendaList);
    });

    this.componentStore.addendaAttributes$.pipe(
      filter((addendaAttributes: IAddendaAttributes) => Boolean(Object.keys(addendaAttributes).length)),
      takeUntil(this.unsubscribe),
    ).subscribe((addendaAttributes: IAddendaAttributes) => {
      this.componentFormProvider.initializeFormData(addendaAttributes);
    });
  }

  editAddendum(): void {
    this.openAddendumSidePanel.emit();
  }

  saveAddendum(): void {
    this.componentStore.saveAddendum(this.componentFormProvider.addendaForm.value);
  }

}

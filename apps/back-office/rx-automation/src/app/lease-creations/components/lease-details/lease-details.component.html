<lease-persons
  [allowAddPerson]="leaseCreationType() === LeaseCreationObjectType.Simple"
/>

@if (unit()) {
  <details-title title="Prospective Unit"/>

  <m-unit-card
    [unit]="unit()"
    [property]="property()"
    [leaseUtilities]="leaseUtilities()"
    [showPropertyStatus]="true"
    [isDeleteButtonVisible]="false"
    [warnings]="warnings()"
  />
}

@if (leaseCreationType() === LeaseCreationObjectType.Renewal && proposal()) {
  <div class="proposals">
    @if (isEditingProposal()) {
      <edit-proposal
        [proposal]="currentProposal()"
        [leaseExpirationType]="leaseExpiration().expirationType"
        [canOnlyEditDates]="true"
      />
    } @else {
      <proposal-card
        title="Accepted Proposal"
        [proposal]="proposal()"
        [expiration]="leaseExpiration()"
        [isEditAvailable]="false"
        [canOnlyEditDates]="true"
      />
    }
  </div>
}

@if (isLeaseSigningWorkflowActive()) {
  <workflow-checklist-table/>
}

<lease-dates-and-fees/>
<lease-utilities/>
<m-lease-benefits-panel
  [unitId]="unitId()"
  [leaseFlowId]="leaseFlowId()"
  [leaseContractId]="leaseCreationId()"
  [defaultInternetEnrollDate]="leaseStartDate()"
  [defaultEnrollStartDate]="defaultEnrollStartDate()"
  [expandedByDefault]="true"
  [disableCollapsing]="true"
/>

<lease-and-addendum [proposal]="proposal()" (openAddendumSidePanel)="onOpenAddendumSidePanel()"/>

@if (leaseCreationType() !== LeaseCreationObjectType.Renewal) {
  <recurring-charges-table [leaseId]="leaseCreationId()"/>
}

@if (leaseDetails()?.leaseId; as leaseFlowId) {
  <m-ledger-table [leaseId]="leaseFlowId"/>
}

@if (addendumSidePanelIsVisible()) {
  <addendum-side-panel (closeAddendumSidePanel)="onCloseAddendumSidePanel()"/>
}

import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { IMyndProposal } from '@myndmanagement/api-leases';
import { MyndUnsubscribeService } from '@myndmanagement/common';
import { IMyndOption } from '@myndmanagement/forms-utils';
import { format, parse, addYears } from 'date-fns';

import { LeaseAndAddendumComponentForm } from '../lease-and-addendum/lease-and-addendum.component.form';

@Component({
  selector: 'concession-addenda',
  templateUrl: './concession-addenda.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MyndUnsubscribeService],
  standalone: false,
})
export class ConcessionAddendaComponent {
  readonly addendaFormProvider = inject(LeaseAndAddendumComponentForm);
  proposal = input<IMyndProposal>();

  readonly monthIntervalsOptions = computed(() => {
    if (this.proposal()) {
      return proposalMonthsIntervalOptions(this.proposal());
    }
    return [];
  });
}

function proposalMonthsIntervalOptions(proposal: IMyndProposal): IMyndOption[] {
  const months: IMyndOption[] = [];
  const start = parse(proposal.leaseStartDate);
  // leaseEndDate can be empty for month to month proposals
  const end = proposal.leaseEndDate ? parse(proposal.leaseEndDate) : addYears(new Date(), 1);

  const endTime = end.getTime();
  const currentDate = start;
  currentDate.setHours(0, 0, 0, 0);
  currentDate.setDate(1);

  while (currentDate.getTime() <= endTime) {
    months.push({
      key: format(currentDate, 'YYYY-MM'),
      value: format(currentDate, 'MM/YYYY'),
    } as IMyndOption);
    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return months;
}

<addenda-panel title="Concession Addendum" addendaKey="concession" [formGroup]="addendaFormProvider.addendaForm">
  <div class="description row" formGroupName="concession">
    <div class="form-item">
      <m-text-input
        formControlName="amount"
        type="currency"
        label="Amount of concession"
        [error]="addendaFormProvider.addendaForm.get('concession.amount').invalid && ' '"
      />
    </div>

    <div class="form-item">
      <m-form-multiselect
        formControlName="months"
        itemDisplayName="value"
        label="Months of concession"
        [items]="monthIntervalsOptions()"
        [error]="addendaFormProvider.addendaForm.get('concession.months').invalid && ' '"
      />
    </div>
  </div>
</addenda-panel>

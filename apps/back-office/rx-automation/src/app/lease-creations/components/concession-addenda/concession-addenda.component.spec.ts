import { ReactiveFormsModule } from '@angular/forms';
import { IMyndProposal } from '@myndmanagement/api-leases';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { createComponentFactory, mockProvider, Spectator } from '@ngneat/spectator/jest';
import { Subject } from 'rxjs';

import { AddendaPanelComponent } from '../addenda-panel/addenda-panel.component';
import { LeaseAndAddendumComponentForm } from '../lease-and-addendum/lease-and-addendum.component.form';
import { LeaseAndAddendumComponentStore } from '../lease-and-addendum/lease-and-addendum.component.store';

import { ConcessionAddendaComponent } from './concession-addenda.component';

describe(ConcessionAddendaComponent.name, () => {
  let spectator: Spectator<ConcessionAddendaComponent>;

  const createComponent = createComponentFactory({
    component: ConcessionAddendaComponent,
    declarations: [AddendaPanelComponent],
    imports: [
      MyndFormsModule,
      ReactiveFormsModule,
    ],
    providers: [
      LeaseAndAddendumComponentForm,
      mockProvider(LeaseAndAddendumComponentStore, {
        addendaDetailsVisibility$: new Subject(),
      }),
    ],
  });

  beforeEach(() => {
    spectator = createComponent({
      props: {
        proposal: { leaseStartDate: '2024-08-16', leaseEndDate: '2025-06-30' } as IMyndProposal,
      },
    });
  });

  it('should return 2 months lease interval', () => {
    const proposal = {
      leaseStartDate: '2000-01-01',
      leaseEndDate: '2000-02-01',
    } as IMyndProposal;
    spectator.setInput('proposal', proposal);

    expect(spectator.component.monthIntervalsOptions())
      .toEqual([{ key: '2000-01', value: '01/2000' }, { key: '2000-02', value: '02/2000' }]);
  });

  it('should include month if it is the last day of it', () => {
    const proposal = {
      leaseStartDate: '2000-01-31',
      leaseEndDate: '2000-02-01',
    } as IMyndProposal;
    spectator.setInput('proposal', proposal);

    expect(spectator.component.monthIntervalsOptions())
      .toEqual([{ key: '2000-01', value: '01/2000' }, { key: '2000-02', value: '02/2000' }]);
  });

  it('should return 13 month', () => {
    const proposal = {
      leaseStartDate: '2000-01-01',
      leaseEndDate: '2001-01-01',
    } as IMyndProposal;
    spectator.setInput('proposal', proposal);

    expect(spectator.component.monthIntervalsOptions())
      .toEqual([
        { key: '2000-01', value: '01/2000' }, { key: '2000-02', value: '02/2000' },
        { key: '2000-03', value: '03/2000' }, { key: '2000-04', value: '04/2000' },
        { key: '2000-05', value: '05/2000' }, { key: '2000-06', value: '06/2000' },
        { key: '2000-07', value: '07/2000' }, { key: '2000-08', value: '08/2000' },
        { key: '2000-09', value: '09/2000' }, { key: '2000-10', value: '10/2000' },
        { key: '2000-11', value: '11/2000' }, { key: '2000-12', value: '12/2000' },
        { key: '2001-01', value: '01/2001' },
      ]);
  });

});

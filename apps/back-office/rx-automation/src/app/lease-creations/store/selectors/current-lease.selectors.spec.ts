import { MyndLeaseSignatureStatus } from '@myndmanagement/api-leases';

import { ICurrentLeaseCreationState } from '../reducers/lease-creation.reducer';

import { selectHasPendingSignature } from './current-lease.selectors';

describe('current lease selectors', () => {
  describe('selectHasPendingSignature', () => {
    it('should return true if lease signers signature in pending status', () => {
      const state = {
        leaseCreationData: {
          leaseSigners: [{ applicantId: '0', signatureStatus: MyndLeaseSignatureStatus.Pending }],
          leaseGuarantors: [{ applicantId: '1', signatureStatus: MyndLeaseSignatureStatus.Signed }],
        },
      } as ICurrentLeaseCreationState;

      expect(selectHasPendingSignature.projector(state)).toBe(true);
    });

    it('should return true if lease guarantors signature in pending status', () => {
      const state = {
        leaseCreationData: {
          leaseSigners: [{ applicantId: '0', signatureStatus: MyndLeaseSignatureStatus.Signed }],
          leaseGuarantors: [{ applicantId: '1', signatureStatus: MyndLeaseSignatureStatus.Pending }],
        },
      } as ICurrentLeaseCreationState;

      expect(selectHasPendingSignature.projector(state)).toBe(true);
    });

    it('should return false if there is no pending signatures', () => {
      const state = {
        leaseCreationData: {
          leaseSigners: [{ applicantId: '0', signatureStatus: MyndLeaseSignatureStatus.Declined }],
          leaseGuarantors: [{ applicantId: '1', signatureStatus: MyndLeaseSignatureStatus.Signed }],
        },
      } as ICurrentLeaseCreationState;

      expect(selectHasPendingSignature.projector(state)).toBe(false);
    });
  });
});

import { MyndLeaseCreationObjectType, MyndLeaseSignatureStatus } from '@myndmanagement/api-leases';
import { MyndManagementStatus } from '@myndmanagement/api-properties';
import { myndOttoLinks } from '@myndmanagement/common-otto';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { isFuture } from 'date-fns';

import { ICurrentLeaseCreationState, leaseCreationStateKey } from '../reducers/lease-creation.reducer';

export const selectLeaseCreationState = createFeatureSelector<ICurrentLeaseCreationState>(leaseCreationStateKey);

export const selectLeaseAddendaList = createSelector(
  selectLeaseCreationState,
  state => state.addendaList,
);

export const selectLeaseAddendaListType = createSelector(
  selectLeaseAddendaList,
  addendaList => addendaList ? addendaList.leaseType : null,
);

export const selectLeaseApplicationDetailsPets = createSelector(
  selectLeaseCreationState,
  state => state.application ? state.application.details.pets : null,
);

export const selectLeaseHistory = createSelector(
  selectLeaseCreationState,
  state => state.history,
);

export const selectLeaseDetails = createSelector(
  selectLeaseCreationState,
  state => state?.leaseDetails,
);

export const selectLeaseStartDate = createSelector(
  selectLeaseCreationState,
  state => state?.leaseDetails?.dates?.leaseStartDate,
);

export const selectLeaseFlowId = createSelector(
  selectLeaseDetails,
  leaseFlow => leaseFlow?.leaseId,
);

export const selectLeaseDates = createSelector(
  selectLeaseDetails,
  leaseDetails => leaseDetails?.dates,
);

export const selectLeaseMoveInDate = createSelector(
  selectLeaseDetails,
  leaseDetails => leaseDetails ? leaseDetails.dates.moveInDate : null,
);

export const selectLeaseExpiration = createSelector(
  selectLeaseCreationState,
  state => state.expiration,
);

export const selectLeaseExpirationId = createSelector(
  selectLeaseExpiration,
  expiration => expiration ? expiration.expirationId : null,
);

export const selectLeaseScore = createSelector(
  selectLeaseCreationState,
  state => state.score,
);

export const selectLeaseUtilities = createSelector(
  selectLeaseCreationState,
  state => state.utilities,
);

// addenda attributes

export const selectLeaseAddendaAttributes = createSelector(
  selectLeaseCreationState,
  state => state.addendaAttributes,
);

// property

export const selectLeaseProperty = createSelector(
  selectLeaseCreationState,
  state => state.property,
);

export const selectLeasePropertyId = createSelector(
  selectLeaseProperty,
  property => property?.propertyId,
);

export const selectLeasePropertyOffboarding = createSelector(
  selectLeaseCreationState,
  state => state.propertyOffboarding,
);

export const selectLeasePropertyAmenities = createSelector(
  selectLeaseCreationState,
  state => state.propertyAmenities,
);

export const selectLeasePropertyOffboardedAt = createSelector(
  selectLeasePropertyOffboarding,
  propertyOffboarding => propertyOffboarding?.managementEndDate,
);

export const selectLeasePropertyStatus = createSelector(
  selectLeasePropertyOffboarding,
  (propertyOffboarding) => {
    if (!propertyOffboarding) {
      return;
    }

    if (propertyOffboarding.managementEndDate) {
      if (isFuture(propertyOffboarding.managementEndDate)) {
        return MyndManagementStatus.Offboarding;
      }

      return MyndManagementStatus.Offboarded;
    }

    if (propertyOffboarding.managementEndDate) {
      return MyndManagementStatus.Managed;
    }

    return;
  },
);

export const selectLeasePropertyInsurance = createSelector(
  selectLeaseProperty,
  property => property?.insurance,
);

export const selectLeasePropertyInsuranceRequired = createSelector(
  selectLeasePropertyInsurance,
  insurance => insurance?.required,
);

export const selectLeasePropertyPets = createSelector(
  selectLeasePropertyAmenities,
  propertyAmenities => propertyAmenities?.pets,
);

export const selectLeasePropertyPetsAllowed = createSelector(
  selectLeasePropertyPets,
  pets => pets?.petsAllowed,
);

// unit

export const selectLeaseUnit = createSelector(
  selectLeaseCreationState,
  state => state.unit,
);

export const selectLeaseUnitId = createSelector(
  selectLeaseUnit,
  unit => unit?.unitId,
);

export const selectLeaseUnitSmartLock = createSelector(
  selectLeaseUnit,
  unit => unit?.amenities.smartLock,
);

export const selectLeaseUnitParkingRent = createSelector(
  selectLeaseUnit,
  unit => unit?.rentalDetails.parkingRent,
);

export const selectLeaseUnitParkingSpace = createSelector(
  selectLeaseUnit,
  unit => unit?.parkingDetails?.parkingSpaceNumber,
);

export const selectLeaseUnitRentalDetails = createSelector(
  selectLeaseUnit,
  unit => unit?.rentalDetails,
);

export const selectLeaseUnitVacantReadyDate = createSelector(
  selectLeaseUnit,
  unit => unit?.rentalDetails?.vacantReadyDate,
);

export const selectPropertyId = createSelector(
  selectLeaseUnit,
  unit => unit?.propertyId,
);

// lease creation object

// flags

export const selectIsAddingNewLeasePerson = createSelector(
  selectLeaseCreationState,
  state => state.isAddingNewPerson,
);

export const selectIsChangeLeaseUnitPanelOpen = createSelector(
  selectLeaseCreationState,
  state => state.isChangeUnitPanelOpen,
);

export const selectIsChangingLeaseEscalatedState = createSelector(
  selectLeaseCreationState,
  state => state.isChangingEscalatedState,
);

export const selectIsChangingLeaseUnit = createSelector(
  selectLeaseCreationState,
  state => state.isChangingUnit,
);

export const selectIsChangingTypeOfPersonId = createSelector(
  selectLeaseCreationState,
  state => state.isChangingTypeOfPersonId,
);

export const selectIsLoadingLease = createSelector(
  selectLeaseCreationState,
  state => state.isLoading,
);

export const selectIsLoadingLeaseComplete = createSelector(
  selectLeaseCreationState,
  state => state.isLoadingComplete,
);

export const selectIsLoadingLeaseHistory = createSelector(
  selectLeaseCreationState,
  state => state.isLoadingHistory,
);

export const selectIsLoadingLeaseMoveIn = createSelector(
  selectLeaseCreationState,
  state => state.isLoadingMoveIn,
);

export const selectIsLoadingSendLease = createSelector(
  selectLeaseCreationState,
  state => state.isLoadingSendLease,
);

export const selectIsResendingEmailMap = createSelector(
  selectLeaseCreationState,
  state => state.isResendingEmailMap,
);

export const selectIsResendingEmailForAll = createSelector(
  selectLeaseCreationState,
  state => state.isResendingEmailForAll,
);

export const selectIsSavingLease = createSelector(
  selectLeaseCreationState,
  state => state.isSaving,
);

// urls

export const selectFutureLeaseUrl = createSelector(
  selectLeaseCreationState,
  (currentLease) => {
    const leaseDetails = currentLease.leaseDetails;
    const lease = currentLease.leaseCreationData;

    if (!leaseDetails || lease?.leaseCreationObject.type === MyndLeaseCreationObjectType.Renewal) {
      return;
    }

    return myndOttoLinks.lease(leaseDetails.leaseId);
  },
);

export const selectCurrentLeaseUrl = createSelector(
  selectLeaseCreationState,
  (currentLease) => {
    const lease = currentLease.leaseCreationData;
    const expiration = currentLease.expiration;

    if (!lease || !expiration || (lease.leaseCreationObject.type !== MyndLeaseCreationObjectType.Renewal)) {
      return;
    }

    return myndOttoLinks.lease(expiration.leaseId);
  },
);

export const selectExpirationLeaseUrl = createSelector(
  selectLeaseCreationState,
  (currentLease) => {
    const lease = currentLease.leaseCreationData;
    const expiration = currentLease.expiration;
    const isRenewal = lease?.leaseCreationObject.type === MyndLeaseCreationObjectType.Renewal;

    if (!isRenewal || !expiration) {
      return;
    }

    return `/lease-expirations/${expiration.expirationId}`;
  },
);

export const selectHasPendingSignature = createSelector(
  selectLeaseCreationState,
  state => [
    ...state.leaseCreationData.leaseSigners,
    ...state.leaseCreationData.leaseGuarantors,
  ].some(signer => signer.signatureStatus === MyndLeaseSignatureStatus.Pending),
);

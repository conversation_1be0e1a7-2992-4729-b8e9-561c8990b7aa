<div [formGroup]="form" novalidate>
  <vd-vendor-panel title="Services">
    @if (!readOnly) {
      <div class="form-checkboxes">
        <m-form-checkbox label="Active" formControlName="active" mValidateControl></m-form-checkbox>
      </div>
      <div class="form-checkboxes">
        <m-form-checkbox label="Mynd IHT Vendor" formControlName="ihtVendor" mValidateControl></m-form-checkbox>
        <m-form-checkbox label="Mynd PA Vendor" formControlName="paVendor" mValidateControl></m-form-checkbox>
        <m-form-checkbox label="R&M Vendor" formControlName="rmVendor" mValidateControl></m-form-checkbox>
      </div>
      <div class="form-checkboxes">
        <m-form-checkbox label="Placeholder Profile" formControlName="placeholderVendor" mValidateControl></m-form-checkbox>
      </div>
      <div class="form-checkboxes">
        <m-select label="Vendor Type" formControlName="vendorType" mValidateControl>
          @for (option of myndVendorTypesOptions; track option.key) {
            <m-option [value]="option.key">{{ option.value }}</m-option>
          }
        </m-select>
      </div>
      @if (form.controls.ihtVendor.value) {
        <div class="form-group">
          <m-autocomplete-multiselect-bo-users
            formControlName="ihtServiceTechniciansBoUsersDto"
            label="Service Technicians"
            [isClearButtonVisible]="true"
            [includeInactive]="false"
            mValidateControl
          ></m-autocomplete-multiselect-bo-users>
          <m-autocomplete-bo-user
            formControlName="ihtDispatcherBoUserDto"
            label="IHT Dispatcher"
            [showClear]="true"
            [includeInactive]="false"
            mValidateControl
          ></m-autocomplete-bo-user>
        </div>
      }
      @if (form.controls.paVendor.value) {
        <div class="form-group">
          <m-autocomplete-multiselect-bo-users
            formControlName="paBoUsersDto"
            label="Property Associates"
            [isClearButtonVisible]="true"
            [includeInactive]="false"
            mValidateControl
          ></m-autocomplete-multiselect-bo-users>
          <m-autocomplete-bo-user
            formControlName="paDispatcherBoUserDto"
            label="PA Dispatcher"
            [showClear]="true"
            [includeInactive]="false"
            mValidateControl
          ></m-autocomplete-bo-user>
        </div>
      }
      <div class="form-group">
        <m-label [counter]="form.value.categories?.length || false">Service Categories</m-label>
        <m-autocomplete-multiselect
          itemDisplayName="value"
          key="key"
          formControlName="categories"
          mValidateControl
          [searchOnOpen]="true"
          [search]="searchCategories"
        ></m-autocomplete-multiselect>
      </div>
      <div class="form-group">
        @if (tags$ | async; as tags) {
          <m-form-tags
            itemDisplayName="name"
            formControlName="tags"
            label="Tags"
            [items]="tags"
            [searchStrategy]="searchTagStrategy"
          ></m-form-tags>
        }
      </div>
      <div class="form-column">
        <m-text-input label="Site Capture Vendor ID" formControlName="siteCaptureVendorId" mValidateControl></m-text-input>
      </div>
    }
    @if (readOnly) {
      <div class="readonly">
        <vd-section-item label="Active?">
          <vd-bool-display [value]="vendor?.active"></vd-bool-display>
        </vd-section-item>
        <vd-section-item label="Last Action Date:">{{
          (vendor?.lastActionAt | mDate: 'datetime') || '&mdash;'
        }}</vd-section-item>
        <vd-section-item label="CSAT:">{{ vendor?.csatRating || '&mdash;' }}</vd-section-item>
        <vd-section-item label="Mynd IHT Vendor?">
          <vd-bool-display [value]="vendor?.ihtVendor"></vd-bool-display>
        </vd-section-item>
        <vd-section-item label="Mynd PA Vendor?">
          <vd-bool-display [value]="vendor?.['paVendor']"></vd-bool-display>
        </vd-section-item>
        <vd-section-item label="R&M Vendor?">
          <vd-bool-display [value]="vendor?.rmVendor"></vd-bool-display>
        </vd-section-item>
        <vd-section-item label="Placeholder Profile?">
          <vd-bool-display [value]="vendor?.placeholderVendor"></vd-bool-display>
        </vd-section-item>
        <vd-section-item label="Vendor Type:">
          {{ (vendor?.vendorType | mMapValue: myndVendorTypes) || MyndValuePlaceholder.Dash }}
        </vd-section-item>
        <vd-section-item label="Site Capture Vendor ID:">
          {{ vendor?.siteCaptureVendorId || MyndValuePlaceholder.Dash }}
        </vd-section-item>
        @if (vendor?.ihtVendor && vendor?.ihtServiceTechniciansBoUsersDto?.length) {
          <div class="service-categories">
            <h3 class="section-subtitle">Service Technicians</h3>
            @for (technician of vendor?.ihtServiceTechniciansBoUsersDto; track technician.boUserId) {
              <div class="category-tag">
                <a [routerLink]="['/technicians', technician.boUserId]">{{technician | mFullName }}</a>
              </div>
            }
          </div>
        }
        @if (vendor?.ihtVendor && vendor?.ihtDispatcherBoUserDto) {
          <div class="service-categories">
            <h3 class="section-subtitle">IHT Dispatcher</h3>
            <div class="category-tag">{{
              vendor?.ihtDispatcherBoUserDto | mFullName
            }}</div>
          </div>
        }
        @if (vendor?.paVendor && vendor?.paBoUsersDto?.length) {
          <div class="service-categories">
            <h3 class="section-subtitle">Property Associates</h3>
            @for (propertyAssociate of vendor?.paBoUsersDto; track propertyAssociate) {
              <div class="category-tag">{{
                propertyAssociate | mFullName
              }}</div>
            }
          </div>
        }
        @if (vendor?.paVendor && vendor?.paDispatcherBoUserDto) {
          <div class="service-categories">
            <h3 class="section-subtitle">PA Dispatcher</h3>
            <div class="category-tag">{{
              vendor?.paDispatcherBoUserDto | mFullName
            }}</div>
          </div>
        }
        @if (vendor?.scores) {
          <vd-vendor-scores [scores]="vendor?.scores"></vd-vendor-scores>
        }
        <div class="service-categories">
          <h3 class="section-subtitle">Service Categories</h3>
          @if (!formCategories?.length) {
            <div>&mdash;</div>
          }
          @for (category of formCategories; track category.key) {
            <div class="category-tag">{{
              category.value
            }}</div>
          }
        </div>
        <div class="tags">
          <h3 class="section-subtitle">Tags</h3>
          @if (!formTags?.length) {
            <div>&mdash;</div>
          }
          @for (tag of formTags; track tag) {
            <div class="tag">{{ tag }}</div>
          }
        </div>
        <m-related-service-requests
          [isMergeAvailable]="false"
          [viewAllQueryParams]="{ vendor: { vendorId: vendor?.vendorId, name: vendor?.name } }"
          [vendorId]="vendor?.vendorId"
        ></m-related-service-requests>
        <m-related-cases [vendorId]="vendor?.vendorId" [isMergeAvailable]="false"></m-related-cases>
      </div>
    }
  </vd-vendor-panel>
</div>

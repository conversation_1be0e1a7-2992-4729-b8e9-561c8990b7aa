import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Requested } from '@myndmanagement/angular-resource-utils';
import { myndAttachmentTypes } from '@myndmanagement/api-attachments';
import { MyndFileAPIService } from '@myndmanagement/api-files';
import { MyndCommonModule } from '@myndmanagement/common';
import { myndSortBasic } from '@myndmanagement/common-utils';
import { MyndFileViewerModule, MyndFileViewerService } from '@myndmanagement/file-manager/viewer';
import { myndHoaObjectSignalStore } from '@myndmanagement/hoa-store';
import { MyndFormsModule } from '@myndmanagement/ui-forms';
import { MyndTableModule } from '@myndmanagement/ui-table';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { finalize, map, tap } from 'rxjs/operators';

import { HoaPanelComponent } from '../ui/panel/hoa-panel.component';

import { HoaUWFileItem } from './data-access/hoa-uw-file-item';
import { HoaUWService } from './data-access/hoa-uw.service';

@Component({
  selector: 'hoa-ai-scan',
  imports: [
    FormsModule,
    HoaPanelComponent,
    MyndTableModule,
    MyndCommonModule,
    MyndFormsModule,
    MyndFileViewerModule,
  ],
  templateUrl: './ai-scan.component.html',
  styleUrl: './ai-scan.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [MyndFileViewerService, MyndFileAPIService],
})
export default class AiScanComponent {
  signalStore = inject(myndHoaObjectSignalStore);
  hoaUWService = inject(HoaUWService);
  fileViewerService = inject(MyndFileViewerService);
  fileAPIService = inject(MyndFileAPIService);
  toastr = inject(MyndToastrService);

  PROMPT_LIMIT = 6000;
  myndAttachmentTypes = myndAttachmentTypes;

  selected = signal<string[]>([]);
  submitted = signal<boolean>(false);
  extract = signal<Requested>(undefined);
  prompt = signal<string>('');
  fileIdToView = signal<string>(undefined);

  hoaId = computed(() => this.signalStore.hoaObject().hoaId);
  isValid = computed(() => this.submitted() ? this.selected().length > 0 : true);
  atts = computed(() => this.attachments.value());
  extractedTextFileIds = computed(() =>
    this.atts()
      .filter(a => a.extractedTextFileId)
      .map(a => a.extractedTextFileId),
  );
  allSelected = computed(() =>
    this.extractedTextFileIds().length > 0 && this.extractedTextFileIds().every(id => this.selected().includes(id)),
  );

  attachments = rxResource<HoaUWFileItem[], string>({
    request: this.hoaId,
    loader: (params) =>
      this.hoaUWService.getAttachments(params.request)
        .pipe(
          map(res => res.sort((a, b) => myndSortBasic(a.filename, b.filename))),
          this.toastr.catchServerError(),
        ),
  });

  rxExtract = rxResource({
    request: this.extract,
    loader: () => {
      const selectedAttIds = this.atts()
        .map(a => a.extractedTextFileId)
        .filter(a => this.selected().some(s => s === a));

      return this.hoaUWService.extract({
        hoaId: this.hoaId(),
        extractedTextFiles: selectedAttIds,
        additionalPrompt: this.prompt(),
      })
        .pipe(
          tap(() => this.toastr.success('Extraction started successfully')),
          this.toastr.catchServerError(),
        );
    },
  });

  rxFileIdToView = rxResource({
    request: this.fileIdToView,
    loader: (params) =>
      this.fileAPIService.getById(params.request)
        .pipe(
          tap(f => this.fileViewerService.open(f)),
          this.toastr.catchServerError(),
          finalize(() => this.fileIdToView.set(undefined)),
        ),
  });

  toggleSelectAll() {
    if (this.allSelected()) {
      this.selected.set([]);
    } else {
      this.selected.set(this.extractedTextFileIds());
    }
  }

  toggleSelection(attachment: HoaUWFileItem) {
    if (!attachment.extractedTextFileId) return;
    const id = attachment.extractedTextFileId;
    const current = this.selected();
    if (current.includes(id)) {
      this.selected.set(current.filter(i => i !== id));
    } else {
      this.selected.set([...current, id]);
    }
  }

  submit() {
    this.submitted.set(true);

    if (this.isValid()) {
      this.extract.set({});
    }
  }
}

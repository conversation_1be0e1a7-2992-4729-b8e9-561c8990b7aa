<hoa-panel title="Documents to Scan">
  <ng-container ngProjectAs="panel-content">
    @if (attachments.isLoading()) {
      <m-throbber size="large" />
    } @else if (atts().length === 0) {
      <span>No documents</span>
    } @else {
      @let setDocumentFirst = 'Failed to extract document. If recently uploaded, please wait, then refresh the page';
      <m-table [isFullWidth]="true">
        <m-table-row [isHeader]="true">
          @let disabled = extractedTextFileIds().length === 0;
          <m-table-column
            [hasPadding]="false"
            [tooltipConfig]="{
              tooltip: disabled ? setDocumentFirst : ''
            }"
          >
            <m-form-checkbox
              [isInTableCell]="true"
              [ngModel]="allSelected()"
              [disabled]="disabled"
              (click)="toggleSelectAll()"
            />
          </m-table-column>
          <m-table-column>Document</m-table-column>
          <m-table-column>Type</m-table-column>
          <m-table-column>Comments</m-table-column>
          <m-table-column>Document Date</m-table-column>
          <m-table-column>Uploaded By</m-table-column>
          <m-table-column>Upload Date</m-table-column>
        </m-table-row>
        @for (attachment of atts(); track attachment.fileId) {
          <m-table-row>
            <m-table-column
              [hasPadding]="false"
              [inlineStyles]="{ width: '40px' }"
              [tooltipConfig]="{
                tooltip: attachment.extractedTextFileId ? '' : setDocumentFirst
              }"
            >
              <m-form-checkbox
                [isInTableCell]="true"
                [ngModel]="selected().includes(attachment.extractedTextFileId)"
                [disabled]="!attachment.extractedTextFileId"
                (click)="toggleSelection(attachment)"
              />
            </m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >
              <a (click)="fileIdToView.set(attachment.fileId)">{{ attachment.filename }}</a>
            </m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >{{ myndAttachmentTypes[attachment.type] }}</m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >{{ attachment.comments }}</m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >{{ attachment.fileDate | mDate }}</m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >{{ attachment.uploadedBy }}</m-table-column>
            <m-table-column
              [inlineStyles]="{ 'max-width': '200px' }"
            >{{ attachment.uploadedAt | mDate }}</m-table-column>
          </m-table-row>
        }
      </m-table>
      <m-text-area
        label="Prompt (Optional)"
        helpText="Optionally provide additional context or instructions for the AI scan process"
        placeholder="Enter a prompt"
        [maxLength]="PROMPT_LIMIT"
        [showClear]="true"
        [rows]="4"
        [isAutoExpand]="true"
        [(ngModel)]="prompt"
      />
      <div class="submit">
        <m-button
          icon="circle-play-sm"
          color="green"
          [loading]="rxExtract.isLoading()"
          (click)="submit()"
        >Start</m-button>
        @if (!isValid()) {
          <div class="error">No documents selected</div>
        }
      </div>
    }
  </ng-container>
</hoa-panel>

import { inject, Injectable } from '@angular/core';
import { myndHoaApiRoot } from '@myndmanagement/api-hoa';
import { MyndHttpService } from '@myndmanagement/http';
import { Observable } from 'rxjs';

import { HoaUWExtractPayload } from './hoa-uw-extract-payload';
import { HoaUWFileItem } from './hoa-uw-file-item';

@Injectable({ providedIn: 'root' })
export class HoaUWService {
  private readonly http = inject(MyndHttpService);
  private readonly baseUrl = `${myndHoaApiRoot}/hoa-uw-automation`;

  getAttachments(hoaId: string): Observable<HoaUWFileItem[]> {
    return this.http.get(`${this.baseUrl}/attachments/${hoaId}`);
  }

  extract(payload: HoaUWExtractPayload) {
    return this.http.post(`${this.baseUrl}/extract`, payload);
  }
}

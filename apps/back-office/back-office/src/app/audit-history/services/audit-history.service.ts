import { inject, Injectable } from '@angular/core';
import { MyndUnitsService } from '@myndmanagement/api-properties';
import { MyndHttpService } from '@myndmanagement/http';
import { forkJoin, Observable, throwError } from 'rxjs';
import { map } from 'rxjs/operators';

import { AuditHistoryEntityType } from '../constants/audit-history-entity-type.constant';
import { IAuditEvent } from '../interfaces/audit-history.interface';

@Injectable()
export class AuditHistoryService extends MyndHttpService {
  private readonly http = inject(MyndHttpService);
  private readonly myndUnitsService = inject(MyndUnitsService);

  getAuditHistory(entityId: string, entityType: AuditHistoryEntityType): Observable<IAuditEvent[]> {
    switch (entityType) {
      case AuditHistoryEntityType.Property:
        return this.getPropertyAuditHistory(entityId);
      case AuditHistoryEntityType.Unit:
        return forkJoin([
          this.myndUnitsService.getUnitAuditHistory(entityId),
          this.getUnitConcessionsAuditHistory(entityId),
          this.getUnitMarketingPostsAuditHistory(entityId),
        ]).pipe(
          map(([unitHistory, concessionsHistory, marketingPostsHistory]) =>
            [...unitHistory, ...concessionsHistory, ...marketingPostsHistory].sort(
              (a, b) =>
                new Date(b.updatedAt || 0).getTime() -
                new Date(a.updatedAt || 0).getTime(),
            ),
          ),
        ) as Observable<IAuditEvent[]>;
      case AuditHistoryEntityType.Opportunity:
        return this.getOpportunityAuditHistory(entityId);
      case AuditHistoryEntityType.Onboarding:
        return this.getOnboardingAuditHistory(entityId);
      default:
        return throwError(`Unsupported audit type: ${entityType}`);
    }
  }

  getPropertyAuditHistory(propertyId: string): Observable<IAuditEvent[]> {
    return this.http.get(`v2/properties/properties/${propertyId}/audit`);
  }

  getOpportunityAuditHistory(opportunityId: string): Observable<IAuditEvent[]> {
    return this.http.get(`v2/owner-onboarding/opportunities/${opportunityId}/audit`);
  }

  getOnboardingAuditHistory(onboardingId: string): Observable<IAuditEvent[]> {
    return this.http.get(`v2/owner-onboarding/onboarding/${onboardingId}/audit`);
  }

  getUnitConcessionsAuditHistory(unitId: string): Observable<IAuditEvent[]> {
    return this.http.get(`v2/prospects/concessions/${unitId}/audit`).pipe(
      map(response => response?.objects || []),
    );
  }

  getUnitMarketingPostsAuditHistory(unitId: string): Observable<IAuditEvent[]> {
    return this.http.get(`v2/prospects/units/${unitId}/marketing-posts/audit`).pipe(
      map(response => response?.objects || []),
    );
  }
}

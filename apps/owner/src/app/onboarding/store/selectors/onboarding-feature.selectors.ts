import { MyndPropertyType } from '@myndmanagement/api-properties';
import { createFeatureSelector, createSelector } from '@ngrx/store';

import { IPropertyForm } from '../../../onboarding-shared/interfaces/onboarding-property-form.interface';
import { selectPropertyFormById } from '../../../onboarding-shared/store/onboarding-property.selectors';
import { requiresFloodDisclosure } from '../../constants/flood-disclosure-states.constant';
import {
  PropertyFlowPath,
  createPropertyMFRSteps,
  createPropertySFHSteps,
} from '../../constants/property-creation-pages-data.constant';
import { IOnboardingFeatureState, onboardingFeatureStateKey } from '../reducers/onboarding-feature.reducer';

export const selectOnboardingModule = createFeatureSelector<IOnboardingFeatureState>(onboardingFeatureStateKey);

const isFloodDisclosureComplete = (propertyForm: IPropertyForm): boolean => {
  if (!requiresFloodDisclosure(propertyForm.address?.state)) {
    return true;
  }

  const floodplain = propertyForm.details?.propertyDisclosures?.floodplain;
  const floodDamage = propertyForm.details?.propertyDisclosures?.floodDamage;

  return floodplain !== null && floodplain !== undefined && floodDamage !== null && floodDamage !== undefined;
};

export const propertyStatusResolvers = {
  [PropertyFlowPath.Address]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.address) && isFloodDisclosureComplete(propertyForm);
  },
  [PropertyFlowPath.Amenities]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.amenities);
  },
  [PropertyFlowPath.Appliances]: (propertyForm: IPropertyForm) => {
    return Boolean(
      propertyForm.details?.appliances || propertyForm.details?.units.some(unit => unit?.appliances),
    );
  },
  [PropertyFlowPath.Utilities]: (propertyForm: IPropertyForm) => {
    return Boolean(
      propertyForm.details?.utilities || propertyForm.details?.units.some(unit => unit?.utilities),
    );
  },
  [PropertyFlowPath.AdditionalDetails]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.additionalDetails);
  },
  [PropertyFlowPath.AccessPoints]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.additionalDetails?.accessPoints);
  },
  [PropertyFlowPath.Warranty]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.warranty);
  },
  [PropertyFlowPath.Insurance]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.propertyInsurance);
  },
  [PropertyFlowPath.Units]: (propertyForm: IPropertyForm) => {
    return Boolean(propertyForm.details?.units?.length && propertyForm.details?.units[0].name);
  },
  [PropertyFlowPath.Documents]: () => {
    return true;
  },
};

export const selectCurrentPropertyOnboardingStep = propertyFormId => createSelector(
  selectPropertyFormById(propertyFormId),
  propertyForm => getCurrentPropertyFormOnboardingStep(propertyForm),
);

export const getCurrentPropertyFormOnboardingStep = (propertyForm: IPropertyForm) => {
  if (!propertyForm) {
    return PropertyFlowPath.Address;
  }

  if (propertyStatusResolvers.utilities(propertyForm)) {
    return PropertyFlowPath.Documents;
  }

  if (propertyStatusResolvers.appliances(propertyForm)) {
    return PropertyFlowPath.Utilities;
  }

  if (propertyStatusResolvers.units(propertyForm) && propertyStatusResolvers.insurance(propertyForm)) {
    return PropertyFlowPath.Appliances;
  }

  if (propertyStatusResolvers.insurance(propertyForm)) {
    return PropertyFlowPath.Units;
  }

  if (propertyStatusResolvers.warranty(propertyForm)) {
    return PropertyFlowPath.Insurance;
  }

  if (propertyStatusResolvers[PropertyFlowPath.AccessPoints](propertyForm)) {
    return PropertyFlowPath.Warranty;
  }

  if (propertyStatusResolvers[PropertyFlowPath.AdditionalDetails](propertyForm)) {
    return PropertyFlowPath.AccessPoints;
  }

  if (propertyStatusResolvers.amenities(propertyForm)) {
    return PropertyFlowPath.AdditionalDetails;
  }

  if (propertyStatusResolvers.address(propertyForm)) {
    return PropertyFlowPath.Amenities;
  }

  return PropertyFlowPath.Address;
};

export const selectPropertyOnboardingSteps = propertyFormId => createSelector(
  selectPropertyFormById(propertyFormId),
  (propertyForm) => {
    if (!propertyForm) {
      return createPropertyMFRSteps;
    }

    if (
      propertyForm?.propertyLeadFormType === MyndPropertyType.SingleFamilyHome &&
      propertyForm?.details?.units.length <= 1
    ) {
      return createPropertySFHSteps;
    }

    return createPropertyMFRSteps;
  },
);

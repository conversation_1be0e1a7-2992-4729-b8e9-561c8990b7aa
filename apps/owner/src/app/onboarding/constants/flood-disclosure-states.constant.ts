/**
 * States that require flood disclosure information during property onboarding.
 */
export const statesWithFloodDisclosureRequirement = ['TX', 'FL'];

/**
 * Checks if a given state requires flood disclosure.
 * @param state - The US state code (e.g., 'TX', 'FL')
 * @returns true if the state requires flood disclosure, false otherwise
 */
export const requiresFloodDisclosure = (state: string): boolean => {
  return statesWithFloodDisclosureRequirement.includes(state);
};

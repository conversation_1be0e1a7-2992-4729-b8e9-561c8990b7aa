import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { IMyndOption, MyndButtonColor } from '@myndmanagement/forms-utils';
import { Store } from '@ngrx/store';
import { isMobile } from 'mobile-device-detect';
import { startWith, takeUntil } from 'rxjs/operators';

import { accessLocations, accessTypes, rekeyHardwareOptions } from '../../../../onboarding-shared/constants/property-access-points.constant';
import {
  IPropertyFormAccessPoint,
  IPropertyFormAccessPointForm,
  emptyPropertyFormAccessPoint,
} from '../../../../onboarding-shared/interfaces/onboarding-property-form.interface';
import { upsertPropertyFormAction } from '../../../../onboarding-shared/store/onboarding-property.actions';
import { customErrorHandler } from '../../../../shared/constants/custom-error-handler.constant';
import { OnboardingPropertyFlowClass } from '../../../classes/onboarding-property-flow-class.class';
import { radioYesNoOptions } from '../../../constants/radio-group-options.constant';
import { OnboardingFeatureStateType } from '../../../store/reducers/onboarding-feature.reducer';
import { replaceEmptyString } from '../../../utils/string-util-fns';

@Component({
  selector: 'o-onboarding-property-access-points',
  templateUrl: './onboarding-property-access-points.component.html',
  styleUrls: ['./onboarding-property-access-points.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class OnboardingPropertyAccessPointsComponent extends OnboardingPropertyFlowClass implements OnInit {
  readonly stepTitle = 'Access points';
  readonly stepDescription = 'Please provide these additional details about your property.';

  readonly customErrorHandler = customErrorHandler;
  readonly yesNoOptions = radioYesNoOptions;

  readonly accessTypes: IMyndOption[] = accessTypes;
  readonly accessLocations: IMyndOption[] = accessLocations;
  readonly rekeyHardwareOptions: IMyndOption[] = rekeyHardwareOptions;
  readonly MyndButtonColor = MyndButtonColor;
  readonly mobile = isMobile;

  get accessPointsArray(): UntypedFormArray {
    return this.form?.controls.accessPoints as UntypedFormArray;
  }

  constructor(
    route: ActivatedRoute,
    router: Router,
    formBuilder: UntypedFormBuilder,
    store: Store<OnboardingFeatureStateType>,
  ) {
    super(route, router, formBuilder, store);
  }

  ngOnInit(): void {
    const additionalDetails = this.property.details.additionalDetails || {};
    const accessPoints = additionalDetails.accessPoints?.length
      ? additionalDetails.accessPoints
      : [{ ...emptyPropertyFormAccessPoint }];

    this.form = this.createForm({
      accessStrategy: [
        additionalDetails.accessStrategy,
        Validators.required,
      ],
      gatedAccess: radioYesNoOptions.find(item => item.key === additionalDetails.gatedAccess),
      accessPoints: this.formBuilder.array(accessPoints.map(accessPoint => this.createForm({
        ...this.accessPointToFormControl(accessPoint),
      }))),
    });

    this.setupConditionalValidation();
  }

  save(): void {
    this.form.markAllAsTouched();
    const { accessStrategy, gatedAccess, accessPoints } = this.form.getRawValue();
    const gatedAccessValue = gatedAccess?.key;

    if (this.form.invalid) {
      return;
    }

    this.store.dispatch(upsertPropertyFormAction({
      propertyForm: {
        details: {
          additionalDetails: {
            accessStrategy: accessStrategy || null,
            gatedAccess: typeof gatedAccessValue === 'boolean' ? gatedAccessValue : null,
            accessPoints: gatedAccessValue ? this.toUpdateModel(accessPoints) : [],
          },
        },
      },
      propertyFormId: this.activatedRoute.snapshot.params.propertyFormId,
    }));
    this.navigateToNextViewAfterSaveComplete();
  }

  addAccessPoint(): void {
    const newAccessPointForm = this.createForm({
      ...this.accessPointToFormControl(emptyPropertyFormAccessPoint),
    });
    this.accessPointsArray.push(newAccessPointForm);

    const isGatedAccess = this.form.get('gatedAccess').value?.key === true;
    if (!isGatedAccess) {
      const accessTypeControl = newAccessPointForm.get('accessTypeV2');
      const accessLocationControl = newAccessPointForm.get('accessLocationV2');
      const accessInstructionsControl = newAccessPointForm.get('accessInstructions');

      accessTypeControl.clearValidators();
      accessLocationControl.clearValidators();
      accessInstructionsControl.clearValidators();

      accessTypeControl.updateValueAndValidity();
      accessLocationControl.updateValueAndValidity();
      accessInstructionsControl.updateValueAndValidity();
    }
  }

  removeAccessPoint(index: number): void {
    this.accessPointsArray.removeAt(index);
  }

  private accessPointToFormControl(accessPoint: IPropertyFormAccessPoint): IPropertyFormAccessPointForm {
    return {
      accessRecordId: accessPoint.accessRecordId,
      accessTypeV2: [
        (accessPoint.accessTypeV2 || accessPoint.accessType) ?? null,
        Validators.required,
      ],
      accessLocationV2: [
        (accessPoint.accessLocationV2 || accessPoint.accessLocation) ?? null,
        Validators.required,
      ],
      accessInstructions: [accessPoint.accessInstructions, Validators.required],
      accessNotes: accessPoint.accessNotes,
    };
  }

  private toUpdateModel(accessPoints: IPropertyFormAccessPoint[]): IPropertyFormAccessPoint[] {
    return accessPoints.map(accessPoint => ({
      ...accessPoint,
      accessTypeV2: accessPoint.accessTypeV2,
      accessLocationV2: accessPoint.accessLocationV2,
      accessInstructions: replaceEmptyString(accessPoint.accessInstructions),
      accessNotes: replaceEmptyString(accessPoint.accessNotes),
    }));
  }

  private setupConditionalValidation(): void {
    this.form.get('gatedAccess').valueChanges.pipe(
      startWith(this.form.get('gatedAccess').value),
      takeUntil(this.unsubscribe),
    ).subscribe((gatedAccessValue: IMyndOption<boolean>) => {
      const isGatedAccess = gatedAccessValue?.key;
      this.updateAccessPointsValidation(isGatedAccess);
    });
  }

  private updateAccessPointsValidation(isRequired: boolean): void {
    this.accessPointsArray.controls.forEach((accessPointControl) => {
      const accessPointGroup = accessPointControl as UntypedFormGroup;
      const accessTypeControl = accessPointGroup.get('accessTypeV2');
      const accessLocationControl = accessPointGroup.get('accessLocationV2');
      const accessInstructionsControl = accessPointGroup.get('accessInstructions');

      if (isRequired) {
        accessTypeControl.setValidators(Validators.required);
        accessLocationControl.setValidators(Validators.required);
        accessInstructionsControl.setValidators(Validators.required);
      } else {
        accessTypeControl.clearValidators();
        accessLocationControl.clearValidators();
        accessInstructionsControl.clearValidators();
      }

      accessTypeControl.updateValueAndValidity();
      accessLocationControl.updateValueAndValidity();
      accessInstructionsControl.updateValueAndValidity();
    });
  }
}

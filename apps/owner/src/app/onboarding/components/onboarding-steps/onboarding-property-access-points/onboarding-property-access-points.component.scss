@import '@myndmanagement/styles/styles/color-palette';
@import '../../../styles/onboarding-page';

:host {
  ::ng-deep {
    m-accordian-panel {
      background-color: $color-mynd-white;
      border: 1px solid $color-mynd-navy-25;
      box-shadow: 0 4px 4px rgba($color-mynd-purple, 0.1);
      border-radius: 5px;
      max-width: 69.6rem;

      m-accordian-panel-header {
        display: flex;
        font-family: $font-poppins;
        font-weight: 600;
        font-size: 2.1rem;
        line-height: 3.2rem;
        padding: 2.4rem;
        color: $color-mynd-green;

        .flex-row {
          justify-content: space-between;
          align-items: center;

          .icon {
            color: $color-mynd-purple;
          }
        }

        .expand-icon.expand-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.8rem;
          margin-right: 2.5rem;
        }
      }

      m-accordian-panel-content {
        padding: 2.4rem;
        background-color: transparent;

        form {
          .row {
            &.no-wrap.no-wrap {
              flex-wrap: nowrap;
              margin-bottom: 0.8rem;
            }
          }
        }
      }
    }
  }

  .form-action-buttons {
    margin-top: 2.5rem;
    margin-bottom: 3.2rem;
  }

  .extended-text-width {
    max-width: 70rem;
  }

  .flex-row {
    display: flex;
  }

  .row-item-3 {
    max-width: 18.9rem;
    width: 100%;
  }

  m-accordian-panel-content {
    .mobile-access-point.mobile-access-point.mobile-access-point.mobile-access-point {
      flex-direction: column;

      m-text-area {
        margin: 0;
      }
    }
  }
}

<o-onboarding-page-content
  [title]="stepTitle"
  [description]="stepDescription"
  [sideImgName]="stepSideImgName"
>
  <div class="section-container">
    <div class="section">
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="row">
          <m-form-radio-button-group
            formControlName="gatedAccess"
            label="Is there a gate or building code needed to access the property?"
            itemDisplayName="value"
            [items]="yesNoOptions"
          ></m-form-radio-button-group>
        </div>
        <ng-container *ngIf="form.value.gatedAccess?.key">
          <m-accordian class="access-points" formArrayName="accessPoints">
            <m-accordian-panel
              *ngFor="let accessPoint of accessPointsArray?.controls; let accessPointIndex = index"
              [expanded]="true"
            >
              <m-accordian-panel-header class="flex-row">
                <div class="flex-row flex1">
                  <div class="text">Access Point {{ accessPointIndex + 1 }}</div>
                  <div class="icon icon-trash" (click)="removeAccessPoint(accessPointIndex)"></div>
                </div>
              </m-accordian-panel-header>
              <m-accordian-panel-content [formGroupName]="accessPointIndex">
                <div class="no-wrap row-group-2 row">
                  <m-select class="full-width" label="Access Type" formControlName="accessTypeV2" mValidateControl>
                    <m-option *ngFor="let item of accessTypes" [value]="item.key">{{ item.value }}</m-option>
                  </m-select>
                  <m-select
                    class="full-width"
                    label="Access Location"
                    formControlName="accessLocationV2"
                    mValidateControl
                  >
                    <m-option *ngFor="let item of accessLocations" [value]="item.key">{{ item.value }}</m-option>
                  </m-select>
                </div>
                <div class="no-wrap row-group-2 row" [class.mobile-access-point]="mobile">
                  <m-text-area
                    class="full-width"
                    formControlName="accessInstructions"
                    rows="6"
                    label="Access Instructions"
                    helpText="Please share access details, including key locations, lockbox codes, door instructions, or any special entry directions. This ensures our team can complete scheduled services smoothly and on time."
                    mValidateControl
                    controlUpdateStrategy="onInput"
                  ></m-text-area>
                  <m-text-area
                    class="full-width"
                    formControlName="accessNotes"
                    rows="6"
                    label="Notes"
                    mValidateControl
                    controlUpdateStrategy="onInput"
                  ></m-text-area>
                </div>
              </m-accordian-panel-content>
            </m-accordian-panel>
          </m-accordian>
        </ng-container>
        <div class="form-action-buttons" *ngIf="form.value.gatedAccess?.key">
          <m-button
            [class.full-width]="mobile"
            [color]="MyndButtonColor.Blue"
            [small]="true"
            [isDisabled]="form.invalid"
            (click)="addAccessPoint()"
            >Add another access point</m-button
          >
        </div>
        <div class="row section-title">Rekeying Upon Vacancy</div>
        <div class="row info-text extended-text-width">
          We will be rekeying the unit upon vacancy,
          whether that is during the onboarding process or at the end of a lease term (if onboarding an occupied unit).
          The initial one-time fee includes all hardware for all exterior doors to be Mynd-standard,
          and all rekeys after that (future vacancies, etc.) will be at a small additional cost.
        </div>
        <div class="row">
          <m-select
            class="row-item-4"
            formControlName="accessStrategy"
            label="Choose which option you would like to move forward with,
            hardware-wise, for all units at this property"
            mValidateControl
          >
            <m-option *ngFor="let item of rekeyHardwareOptions" [value]="item.key">{{ item.value }}</m-option>
          </m-select>
        </div>
        <div class="row">
          <o-onboarding-footer-actions
            class="row-item-4"
            [backRouterLink]="prevPath$ | async"
            trackClickEventName="Save Property Access Points"
            [submitDisabled]="isLoading$ | async"
            [submitLoading]="isSaving$ | async"
          ></o-onboarding-footer-actions>
        </div>
      </form>
    </div>
  </div>
</o-onboarding-page-content>

<o-onboarding-page-content
  [title]="stepTitle"
  [description]="stepDescription"
  [sideImgName]="stepSideImgName"
>
  <div class="section-container">
    <div class="section">
      <form
        [formGroup]="form"
        (ngSubmit)="save()"
      >
        <div class="row">
          <m-autocomplete-address
            class="row-item-2"
            formControlName="address"
            label="Street address"
            mValidateControl
            (selectOption)="addressSelected($any($event))"
          >
          </m-autocomplete-address>

          <m-text-input
            class="row-item-2"
            formControlName="city"
            label="City"
            mValidateControl
            controlUpdateStrategy="onInput"
          >
          </m-text-input>
        </div>

        <div
          class="row"
          [class.row-group-2]="mobile"
        >
          <m-select
            class="row-item-2"
            label="State"
            formControlName="state"
            mValidateControl
          >
            <m-option
              *ngFor="let state of states"
              [value]="state.key"
            >{{ state.value }}</m-option>
          </m-select>

          <m-text-input
            class="row-item-2"
            formControlName="zip"
            label="Zip Code"
            mValidateControl
            controlUpdateStrategy="onInput"
          >
          </m-text-input>
        </div>

        @if (statesWithFloodDisclosure.includes(form.get('state').value)) {
          <ng-container [formGroup]="floodAddendumForm">
            <div class="row">
              <m-v2-form-field
                class="form-field"
              >
                <m-v2-form-label>
                  Is this property in a 100-year Floodplain?
                </m-v2-form-label>

                <m-v2-radio-group formControlName="floodplain">
                  <m-v2-radio-button [value]="true">Yes</m-v2-radio-button>
                  <m-v2-radio-button [value]="false">No</m-v2-radio-button>
                </m-v2-radio-group>
              </m-v2-form-field>
            </div>

            <div class="row">
              <m-v2-form-field
                class="form-field"
              >
                <m-v2-form-label>
                  Has there been flood damage in the past 5 years (non-plumbing related)?
                </m-v2-form-label>

                <m-v2-radio-group formControlName="floodDamage">
                  <m-v2-radio-button [value]="true">Yes</m-v2-radio-button>
                  <m-v2-radio-button [value]="false">No</m-v2-radio-button>
                </m-v2-radio-group>
              </m-v2-form-field>
            </div>
          </ng-container>
        }

        <div class="row">
          <o-onboarding-footer-actions
            class="row-item-4"
            [backRouterLink]="prevPath$ | async"
            trackClickEventName="Save Property Address"
            [submitDisabled]="isLoading$ | async"
            [submitLoading]="isSaving$ | async"
          ></o-onboarding-footer-actions>
        </div>
      </form>
    </div>
  </div>
</o-onboarding-page-content>

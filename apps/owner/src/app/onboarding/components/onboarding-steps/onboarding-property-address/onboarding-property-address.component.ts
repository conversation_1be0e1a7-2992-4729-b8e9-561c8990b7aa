import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MyndPropertyType } from '@myndmanagement/api-properties';
import { IMyndMailingAddress } from '@myndmanagement/common-utils';
import { myndUSRegions } from '@myndmanagement/forms-utils';
import { Store } from '@ngrx/store';
import { first, takeUntil } from 'rxjs/operators';

import {
  createPropertyFormAction,
  upsertPropertyFormAction,
} from '../../../../onboarding-shared/store/onboarding-property.actions';
import { defaultMaxLength } from '../../../../shared/constants/validation.constant';
import { getLastActivatedRoute } from '../../../../shared/utils/route-fns';
import { OnboardingPropertyFlowClass } from '../../../classes/onboarding-property-flow-class.class';
import { statesWithFloodDisclosureRequirement, requiresFloodDisclosure } from '../../../constants/flood-disclosure-states.constant';
import { OnboardingStepKey, onboardingStepsMap } from '../../../constants/onboarding-steps.constant';
import { IOnboardingSection } from '../../../interfaces/onboarding-section.interface';
import { OnboardingFeatureStateType } from '../../../store/reducers/onboarding-feature.reducer';
import { selectOnboardingId } from '../../../store/selectors/onboarding.selectors';

@Component({
  selector: 'o-onboarding-property-address',
  templateUrl: './onboarding-property-address.component.html',
  styleUrls: ['./onboarding-property-address.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class OnboardingPropertyAddressComponent extends OnboardingPropertyFlowClass implements OnInit {
  readonly stepTitle = 'Property address';
  readonly stepDescription = 'Now, let\'s start adding the property  you\'ll be renting out!';

  readonly states = myndUSRegions;
  readonly onboardingStep: IOnboardingSection = onboardingStepsMap.get(OnboardingStepKey.CreateProperty);
  readonly onboardingId$ = this.store.select(selectOnboardingId);
  readonly statesWithFloodDisclosure = statesWithFloodDisclosureRequirement;

  floodAddendumForm: UntypedFormGroup;

  private newProperty: boolean;

  constructor(
    activatedRoute: ActivatedRoute,
    router: Router,
    formBuilder: UntypedFormBuilder,
    store: Store<OnboardingFeatureStateType>,
    private cdr: ChangeDetectorRef,
  ) {
    super(activatedRoute, router, formBuilder, store);
  }

  ngOnInit(): void {
    getLastActivatedRoute(this.activatedRoute).data.pipe(
      takeUntil(this.unsubscribe),
    ).subscribe((data) => {
      this.newProperty = data.newProperty;
    });

    this.form = this.createForm({
      address: [null, Validators.compose([Validators.required, Validators.maxLength(defaultMaxLength)])],
      city: [null, Validators.compose([Validators.required, Validators.maxLength(defaultMaxLength)])],
      state: [null, Validators.compose([Validators.required, Validators.maxLength(20)])],
      zip: [null, Validators.compose([Validators.required, Validators.maxLength(20)])],
    });

    this.floodAddendumForm = this.createForm({
      floodplain: [null],
      floodDamage: [null],
    });

    if (this.property) {
      this.patchForm(this.property.address);
    }

    if (this.property?.details?.propertyDisclosures) {
      this.floodAddendumForm.patchValue(this.property?.details?.propertyDisclosures);
    }

    this.form.get('state').valueChanges.pipe(
      takeUntil(this.unsubscribe),
    ).subscribe((state: string) => {
      this.updateFloodDisclosureValidators(state);
    });

    if (this.form.get('state').value) {
      this.updateFloodDisclosureValidators(this.form.get('state').value);
    }
  }

  private updateFloodDisclosureValidators(state: string): void {
    const floodplainControl = this.floodAddendumForm.get('floodplain');
    const floodDamageControl = this.floodAddendumForm.get('floodDamage');

    if (requiresFloodDisclosure(state)) {
      floodplainControl.setValidators([Validators.required]);
      floodDamageControl.setValidators([Validators.required]);
    } else {
      floodplainControl.clearValidators();
      floodDamageControl.clearValidators();
      floodplainControl.setValue(null);
      floodDamageControl.setValue(null);
    }

    floodplainControl.updateValueAndValidity();
    floodDamageControl.updateValueAndValidity();
  }

  addressSelected(address: IMyndMailingAddress | string): void {
    if (address) {
      this.form.patchValue(address as IMyndMailingAddress);
      this.cdr.detectChanges();
    }
  }

  save(): void {
    this.form.markAllAsTouched();
    this.floodAddendumForm.markAllAsTouched();

    if (this.form.invalid || this.floodAddendumForm.invalid) {
      return;
    }

    const address = this.form.getRawValue();
    const shouldIncludeFloodDisclosure = requiresFloodDisclosure(address.state);
    const floodAddendum = {
      details: {
        ...(this.property?.details || null),
        propertyDisclosures: this.floodAddendumForm.getRawValue(),
      },
    };

    if (this.newProperty) {
      this.onboardingId$.pipe(
        first<string>(Boolean),
      ).subscribe((onboardingId) => {
        this.store.dispatch(createPropertyFormAction({
          onboardingId,
          propertyForm: {
            address,
            propertyLeadFormType: MyndPropertyType.MultiFamily,
            ...(shouldIncludeFloodDisclosure && floodAddendum),
          },
        }));
      });

    } else {
      this.navigateToNextViewAfterSaveComplete();

      this.store.dispatch(upsertPropertyFormAction({
        propertyForm: {
          address,
          ...(shouldIncludeFloodDisclosure && floodAddendum),
        },
        propertyFormId: this.activatedRoute.snapshot.params.propertyFormId,
      }));
    }
  }
}

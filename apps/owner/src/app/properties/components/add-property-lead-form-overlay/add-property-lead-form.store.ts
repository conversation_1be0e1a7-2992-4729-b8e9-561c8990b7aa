import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { IMyndCommandResult } from '@myndmanagement/http';
import { MyndToastrService } from '@myndmanagement/ui-toast';
import { ComponentStore } from '@ngrx/component-store';
import { tapResponse } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

import { requiresFloodDisclosure } from '../../../onboarding/constants/flood-disclosure-states.constant';
import { IPropertyForm } from '../../../onboarding-shared/interfaces/onboarding-property-form.interface';
import { OnboardingPropertyService } from '../../../onboarding-shared/services/api/property-onboarding.service';
import { ILeadForm } from '../../../properties-shared/interfaces/lead-form.interface';
import { LeadFormApiService } from '../../../properties-shared/services/api/lead-form.service';
import {
  resetMixedPropertiesLoadingCompletion,
} from '../../../properties-shared/store/actions/mixed-properties.actions';
import { IMixedPropertiesSlice } from '../../../properties-shared/store/reducers/mixed-properties.reducer';
import { defaultOnboardingServerErrorMessage } from '../../../shared/constants/default-error-messages.constant';

export interface ILeadFormPageState {
  loading: boolean;
}

const initialState: ILeadFormPageState = {
  loading: false,
};

@Injectable()
export class LeadFormStore extends ComponentStore<ILeadFormPageState> {
  readonly isLoading$ = this.select(state => state.loading);

  readonly createLeadForm = this.effect(
    (trigger$: Observable<ILeadForm>) => trigger$.pipe(
      tap(() => this.setLoadingState(true)),
      switchMap(leadForm => this.leadFormApiService.createLeadForm(leadForm)),
      switchMap(
        ({ result: onboardingId }: IMyndCommandResult) => this.onboardingPropertyService.getPropertyForms(onboardingId),
      ),
      tapResponse(
        ([form]: IPropertyForm[]) => {
          this.mixedPropertiesStore.dispatch(resetMixedPropertiesLoadingCompletion());

          if (requiresFloodDisclosure(form.address.state)) {
            this.router.navigate([`onboarding/${form.onboardingId}/create-property/address/${form.onboardingPropertyFormId}`]);
          } else {
            this.router.navigate([`onboarding/${form.onboardingId}/create-property/amenities/${form.onboardingPropertyFormId}`]);
          }

          this.setLoadingState(false);
        },
        (error: HttpErrorResponse) => {
          if (error.status === HttpStatusCode.PreconditionFailed) {
            this.toast.serverError(error);
          } else {
            this.toast.error({
              message: defaultOnboardingServerErrorMessage,
              statusCode: error.status,
            });
          }
          this.router.navigate([], { queryParams: {} });
          this.setLoadingState(false);
        },
      ),
    ));

  private readonly setLoadingState = this.updater((state, loading: boolean) => ({ ...state, loading }));

  constructor(
    private leadFormApiService: LeadFormApiService,
    private onboardingPropertyService: OnboardingPropertyService,
    private router: Router,
    private toast: MyndToastrService,
    private mixedPropertiesStore: Store<IMixedPropertiesSlice>,
  ) {
    super(initialState);
  }
}

<div class="move-out-page">
  <div class="move-out-page-container">
    <div class="move-out-page-content">
      <p class="move-out-page-text text-centered">
        Select up to <b>3 potential time slots</b> for your pre-move-out inspection
      </p>

      @if (timeSlots().length) {
        <m-time-slot-picker
          [selectionMode]="MyndSelectionMode.Multiple"
          [formControl]="timeSlotsControl"
          [timeSlots]="timeSlots()"
        />
      }

      <div class="warning check-warning">
        <div class="icon icon-triange-exclamation"></div>
        <div>
          The pre move out inspection can be completed no earlier than 14 days before the resident vacate date.
        </div>
      </div>
    </div>
  </div>
</div>

<r-move-out-wizard-progress [showBackButton]="true">
  <button
    mV2Button
    data-testid="confirm-availability-button"
    [stretched]="true"
    [color]="MyndButtonColors.Primary"
    [disabled]="!timeSlotsControl.value?.length"
    (click)="onNextBtnClicked()"
  >Confirm your availability</button>
</r-move-out-wizard-progress>
